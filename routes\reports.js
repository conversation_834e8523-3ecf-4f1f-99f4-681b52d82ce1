const express = require('express');
const { getReport }    = require('../controllers/reportsController');
const { sendDailyReport } = require('../controllers/reportJob');

const router = express.Router();

// GET /api/reports?start=…&end=…
router.get('/', getReport);

// POST /api/reports/send
router.post('/send', async (req, res) => {
  try {
    await sendDailyReport();
    res.json({ ok: true, message: 'Report harian berhasil dikirim.' });
  } catch (err) {
    console.error('❌ Error kirim report manual:', err);
    res.status(500).json({ ok: false, error: 'Gagal mengirim report harian.' });
  }
});

module.exports = router;
