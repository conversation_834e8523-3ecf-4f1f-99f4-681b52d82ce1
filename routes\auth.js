const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');

router.post('/register', authController.register);
router.post('/login', authController.login);
router.get('/profile', authController.verifyToken, authController.getUserProfile);
router.post('/logout', authController.logout);
router.get('/check-token', authController.checkToken); // ✅ Endpoint baru

module.exports = router;
