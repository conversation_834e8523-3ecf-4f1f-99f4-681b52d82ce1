const express = require("express");
const multer = require("multer");
const net = require("net");

const router = express.Router();

require("dotenv").config();

const upload = multer({ storage: multer.memoryStorage() });

router.post("/print-raw", upload.single("raw"), (req, res) => {
  const printerIp = process.env.PRINTER_IP;
  const printerPort = parseInt(process.env.PRINTER_PORT);

  if (!req.file || !req.file.buffer) {
    return res.status(400).send("❌ Tidak ada data ESC/POS yang dikirim.");
  }

  const client = new net.Socket();

  client.connect(printerPort, printerIp, () => {
    console.log("🖨️ Terhubung ke printer. Mengirim data...");
    client.write(req.file.buffer);
    client.end();
  });

  client.on("close", () => {
    console.log("✅ Data berhasil dikirim ke printer.");
    res.send("✅ ESC/POS data berhasil dicetak.");
  });

  client.on("error", (err) => {
    console.error("❌ Gagal mengirim ke printer:", err.message);
    res.status(500).send("❌ Gagal kirim ke printer: " + err.message);
  });
});

module.exports = router;
