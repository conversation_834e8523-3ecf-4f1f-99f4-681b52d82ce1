const cron = require("node-cron");
const db = require("../config/db");
const { sendR<PERSON>yCommandHelper } = require("../controllers/relayController");

const dayjs = require("dayjs");
require("log-timestamp")(function () {
  return `[${dayjs().format("YYYY-MM-DD HH:mm:ss")}]`;
});

///////////////////////////////////////////////////////////////////////////////
// ?? CRON: Memulai Order Hari Ini (status 0 ? 1) jika start_time <= now     //
///////////////////////////////////////////////////////////////////////////////
cron.schedule("* * * * *", async () => {
  console.log("[CRON] ?? Checking for orders to start...");

  try {
    const now = new Date();
    const nowStr = now.toTimeString().split(" ")[0]; // "HH:MM:SS"
    const today = new Date().toISOString().split("T")[0]; // "YYYY-MM-DD"

    const [orders] = await db.query(
      `
      SELECT o.id, o.table_id, o.start_time, t.relay_id, r.relay_name
      FROM billiard_orders o
      JOIN billiard_tables t ON o.table_id = t.id
      LEFT JOIN relay r ON t.relay_id = r.id
      WHERE o.status = 0
        AND o.start_time <= ?
        AND DATE(o.created_at) = ?
    `,
      [nowStr, today]
    );

    for (const order of orders) {
      console.log(`[CRON] ?? Memulai order ${order.id}`);
      await startOrder(
        order.id,
        order.relay_id,
        order.relay_name,
        order.table_id
      );
    }
  } catch (error) {
    console.error("[CRON] ? Error in starting orders:", error);
  }
});

////////////////////////////////////////////////////////////////////////////////
// ?? CRON: Selesaikan Order Hari Ini (status 1 ? 2) jika end_time <= now     //
////////////////////////////////////////////////////////////////////////////////
cron.schedule("* * * * *", async () => {
  console.log("[CRON] ?? Checking for orders to complete...");

  try {
    const now = new Date();
    const nowStr = now.toTimeString().split(" ")[0];
    const today = new Date().toISOString().split("T")[0];

    const [orders] = await db.query(
      `
      SELECT o.id, o.table_id, o.end_time, t.relay_id, r.relay_name
      FROM billiard_orders o
      JOIN billiard_tables t ON o.table_id = t.id
      LEFT JOIN relay r ON t.relay_id = r.id
      WHERE o.status = 1
        AND o.end_time IS NOT NULL
        AND o.end_time <= ?
        AND DATE(o.updated_at) = ?
        AND NOT EXISTS (
          SELECT 1 FROM billiard_orders o2
          WHERE o2.table_id = o.table_id
            AND o2.status = 1
            AND o2.end_time > ?
            AND o2.id != o.id
        )
      ORDER BY o.updated_at DESC
      LIMIT 1
    `,
      [nowStr, today, nowStr]
    );

    for (const order of orders) {
      console.log(`[CRON] ?? Menyelesaikan order ${order.id}`);
      await completeOrder(
        order.id,
        order.relay_id,
        order.relay_name,
        order.table_id
      );
    }
  } catch (error) {
    console.error("[CRON] ? Error in completing orders:", error);
  }
});

////////////////////////////////////////////////////////////////////////
// ?? CRON: Cek session yang sudah lewat, lalu update status ordernya //
////////////////////////////////////////////////////////////////////////
cron.schedule("* * * * *", async () => {
  console.log("[CRON] ?? Memeriksa session yang sudah lewat hari ini...");

  try {
    const today = new Date().toISOString().split("T")[0];

    // ?? Ambil order yang masih aktif (status 0 atau 1) dan session-nya lewat
    const [orders] = await db.query(
      `
        SELECT o.id
        FROM billiard_orders o
        JOIN sessions s ON o.session_id = s.id
        WHERE o.status IN (0, 1)
          AND s.date < ?
      `,
      [today]
    );

    if (orders.length === 0) {
      console.log("? Tidak ada order yang perlu ditandai selesai.");
      return;
    }

    // ?? Update per order
    for (const order of orders) {
      await db.query(`UPDATE billiard_orders SET status = 2 WHERE id = ?`, [
        order.id,
      ]);

      console.log(
        `? Order ${order.id} ditandai sebagai selesai (karena session lewat).`
      );
    }
  } catch (error) {
    console.error(
      "? Gagal memperbarui order dari session yang sudah lewat:",
      error
    );
  }
});

///////////////////////////////////////
// ? START ORDER FUNCTION          //
///////////////////////////////////////
async function startOrder(orderId, relayId, relayName, tableId) {
  const connection = await db.getConnection();
  try {
    await connection.beginTransaction();

    // 1) Lock the order row first
    await connection.query(
      "SELECT id FROM billiard_orders WHERE id = ? FOR UPDATE",
      [orderId]
    );
    // 2) Then lock the table row
    if (tableId) {
      await connection.query(
        "SELECT id FROM billiard_tables WHERE id = ? FOR UPDATE",
        [tableId]
      );
    }

    // 3) Now perform your updates in the same locked order
    await connection.query(
      "UPDATE billiard_orders SET status = 1 WHERE id = ?",
      [orderId]
    );
    if (tableId) {
      await connection.query(
        "UPDATE billiard_tables SET status = 1 WHERE id = ?",
        [tableId]
      );
    }
    if (relayId) {
      await connection.query("UPDATE relay SET status = 1 WHERE id = ?", [
        relayId,
      ]);
      //await sendRelayCommandHelper(relayId, "on");
    }

    await connection.commit();
    console.log(`? Order ${orderId} dimulai, meja ${tableId} digunakan.`);
  } catch (err) {
    await connection.rollback();
    console.error(`? Gagal memulai order ${orderId}:`, err);
    throw err;  // so your withRetry sees it
  } finally {
    connection.release();
  }
}

async function completeOrder(orderId, relayId, relayName, tableId) {
  const connection = await db.getConnection();
  try {
    await connection.beginTransaction();

    // 1) Lock the order
    await connection.query(
      "SELECT id FROM billiard_orders WHERE id = ? FOR UPDATE",
      [orderId]
    );
    // 2) Then lock the table
    if (tableId) {
      await connection.query(
        "SELECT id FROM billiard_tables WHERE id = ? FOR UPDATE",
        [tableId]
      );
    }

    // 3) Now do the updates
    await connection.query(
      "UPDATE billiard_orders SET status = 2 WHERE id = ?",
      [orderId]
    );
    if (tableId) {
      await connection.query(
        "UPDATE billiard_tables SET status = 0 WHERE id = ?",
        [tableId]
      );
    }
    if (relayId) {
      await connection.query("UPDATE relay SET status = 0 WHERE id = ?", [
        relayId,
      ]);
      //await sendRelayCommandHelper(relayId, "off");
    }

    await connection.commit();
    console.log(`? Order ${orderId} selesai, meja ${tableId} dirilis.`);
  } catch (err) {
    await connection.rollback();
    console.error(`? Gagal menyelesaikan order ${orderId}:`, err);
    throw err;
  } finally {
    connection.release();
  }
}
