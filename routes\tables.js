const express = require("express");
const router = express.Router();
const tablesController = require("../controllers/tablesController");

router.get("/", tablesController.getAllTables);
router.get("/:id", tablesController.getTableById);
router.post("/", tablesController.createTable);
router.put("/:id", tablesController.updateTable);
router.delete("/:id", tablesController.deleteTable);

// ✅ Tambahan untuk update relay status berdasarkan ID meja
router.put("/:id/relay-status", tablesController.updateRelayStatus);

module.exports = router;
