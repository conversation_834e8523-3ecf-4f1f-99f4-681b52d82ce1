const cron = require("node-cron");
const db = require("../config/db");

cron.schedule("* * * * *", async () => {
  try {
    await db.query(`
      UPDATE billiard_tables t
      JOIN billiard_orders o ON t.id = o.table_id
      SET t.status = 1, o.status = 1
      WHERE t.status = 0 AND o.status = 0 AND o.end_time > NOW()
    `);

    await db.query(`
      UPDATE billiard_tables t
      JOIN billiard_orders o ON t.id = o.table_id
      SET t.status = 0, o.status = 0
      WHERE t.status = 1 AND o.status = 1 AND o.end_time <= NOW()
    `);

    console.log("✅ Table and order status updated based on rental expiry");
  } catch (error) {
    console.error("❌ Cron Job error:", error);
  }
});
