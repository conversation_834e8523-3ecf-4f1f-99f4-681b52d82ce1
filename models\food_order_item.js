'use strict';
module.exports = (sequelize, DataTypes) => {
  const FoodOrderItem = sequelize.define('FoodOrderItem', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    food_order_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    menu_item_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    price_each: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: false
    },
    subtotal: {
      type: DataTypes.DECIMAL(10,2),
      allowNull: false,
      defaultValue: 0.00
    }
  }, {
    tableName: 'food_order_items',
    underscored: true,
    timestamps: true
  });

  FoodOrderItem.associate = models => {
    FoodOrderItem.belongsTo(models.FoodOrder, { foreignKey: 'food_order_id' });
    FoodOrderItem.belongsTo(models.MenuItem,  { foreignKey: 'menu_item_id'  });
  };

  return FoodOrderItem;
};
