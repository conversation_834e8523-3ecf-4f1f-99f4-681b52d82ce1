const express           = require('express');
const router            = express.Router();
const holidayController = require('../controllers/holidayController');

// GET    /api/holidays          → list semua hari libur
// POST   /api/holidays          → tambah hari libur baru
// GET    /api/holidays/:id      → ambil detail 1 hari libur
// PUT    /api/holidays/:id      → update hari libur
// DELETE /api/holidays/:id      → hapus hari libur
router.get('/',          holidayController.getAllHolidays);
router.post('/',         holidayController.createHoliday);
router.get('/:id',       holidayController.getHolidayById);
router.put('/:id',       holidayController.updateHoliday);
router.delete('/:id',    holidayController.deleteHoliday);

module.exports = router;
