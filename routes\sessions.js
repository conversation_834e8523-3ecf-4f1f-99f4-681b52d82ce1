const express = require('express');
const router = express.Router();
const sessions = require('../controllers/sessionsController');

// sessions master
router.get('/', sessions.getAll);
router.get('/:id', sessions.getById);
router.post('/', sessions.create);
router.put('/:id', sessions.update);
router.delete('/:id', sessions.remove);
// setelah router.post('/sessions', ...), dst.
router.post('/:id/print', sessions.markPrinted);


// its items
// router.get('/:sessionId/items', items.getItems);
// router.post('/:sessionId/items', items.addItem);
// router.put('/items/:id', items.updateItem);
// router.delete('/items/:id', items.removeItem);

module.exports = router;
