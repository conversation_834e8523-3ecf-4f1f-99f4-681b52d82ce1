// controllers/sessionController.js
const db = require("../config/db");
const moment = require("moment");

function toWIBISOString(date) {
  // offset dalam menit untuk WIB (GMT+7)
  const wibOffsetMs = 7 * 60 * 60 * 1000;
  const wibDate = new Date(date.getTime() + wibOffsetMs + 1);
  return wibDate.toISOString().slice(0, 19);
}

// you can move this into a shared utils file if you like
async function insertRelayLog({
  orderId = null,
  tableNumber = null,
  relayNumber,
  action,    // 'ON' or 'OFF'
  status,    // 'SUCCESS' or 'FAILED'
  customerName = null,
  errorMessage = null
}) {
  const sql = `
    INSERT INTO relay_logs
      (order_id, table_number, relay_number, action, status, customer_name, error_message)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `;
  await db.query(sql, [
    orderId,
    tableNumber,
    relayNumber,
    action,
    status,
    customerName,
    errorMessage
  ]);
}

const getAll = async (req, res) => {
  try {
    const [sessions] = await db.query(`
      SELECT
        s.id,
        s.guest_name,
        s.guest_phone,
        s.invoice_number,
        s.date,
        s.status,
        s.discount_percent,
        s.tax_rate,
        s.ppn_rate,
        s.pay_amount,
        s.paid_at,
        s.printed_at,
        s.payment_method,
        s.total_food,
        s.total_billiard,
        s.discount_amount,
        s.tax_amount,
        s.total_amount,
        s.created_at,
        COALESCE(f.food_items, JSON_ARRAY())       AS food_items,
        COALESCE(b.billiard_orders, JSON_ARRAY())  AS billiard_orders
      FROM sessions AS s
      LEFT JOIN (
        SELECT 
          foi.session_id,
          JSON_ARRAYAGG(
            JSON_OBJECT(
              'id',         foi.id,
              'food_order_id', foi.session_id,
              'food_id',    foi.food_id,
              'food_name',  f.name,
              'batch',      foi.batch,
              'quantity',   foi.quantity,
              'price_each', foi.price_each,
              'subtotal',   foi.subtotal
            )
          ) AS food_items
        FROM food_order_items AS foi
        LEFT JOIN foods AS f ON f.id = foi.food_id
        GROUP BY foi.session_id
      ) AS f ON f.session_id = s.id

      LEFT JOIN (
        SELECT
          bo.session_id,
          JSON_ARRAYAGG(
            JSON_OBJECT(
              'id',                      bo.id,
              'table_id',                bo.table_id,
              'table_number',            t.table_number,
              'session_id',              bo.session_id,
              'package_id',              bo.package_id,
              'duration',                bo.duration,
              'price',                   bo.price,
              'discount',                bo.discount,
              'status',                  bo.status,
              'type_package_price_id',   bo.type_package_price_id,
              'start_time',              DATE_FORMAT(bo.start_time, '%Y-%m-%dT%T'),
              'end_time',                DATE_FORMAT(bo.end_time,   '%Y-%m-%dT%T')
            )
          ) AS billiard_orders
        FROM billiard_orders AS bo
        JOIN billiard_tables  AS t  ON t.id = bo.table_id
        GROUP BY bo.session_id
      ) AS b ON b.session_id = s.id

      ORDER BY s.created_at DESC
    `);

    const result = sessions.map((s) => {
      const foodItems = Array.isArray(s.food_items)
        ? s.food_items
        : JSON.parse(s.food_items || "[]");
      const billiardOrders = Array.isArray(s.billiard_orders)
        ? s.billiard_orders
        : JSON.parse(s.billiard_orders || "[]");

      const now = new Date();
      const updatedBilliardOrders = billiardOrders.map((order) => {
        const start = order.start_time ? new Date(order.start_time) : null;
        let end = order.end_time ? new Date(order.end_time) : null;
        let adjustedEnd = end;
        if (start && end && end < start) {
          adjustedEnd = new Date(end);
          adjustedEnd.setDate(adjustedEnd.getDate() + 1);
        }
        const remaining =
          start && adjustedEnd && now >= start && now <= adjustedEnd
            ? Math.max(Math.floor((adjustedEnd - now) / 1000), 0)
            : 0;

        return {
          ...order,
          end_time: toWIBISOString(adjustedEnd),
          remaining_time_seconds: remaining,
        };
      });

      return {
        ...s,
        food_items: foodItems,
        billiard_orders: updatedBilliardOrders,
      };
    });

    res.json(result);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
};

// controllers/sessionController.js
const getById = async (req, res) => {
  const { id } = req.params;
  try {
    const [[sessions]] = await db.query(`
      SELECT
        s.id,
        s.guest_name,
        s.guest_phone,
        s.invoice_number,
        s.date,
        s.status,
        s.discount_percent,
        s.tax_rate,
        s.ppn_rate,
        s.pay_amount,
        s.paid_at,
        s.printed_at,
        s.payment_method,
        s.total_food,
        s.total_billiard,
        s.discount_amount,
        s.tax_amount,
        s.total_amount,
        s.created_at,
        COALESCE(f.food_items, JSON_ARRAY())       AS food_items,
        COALESCE(b.billiard_orders, JSON_ARRAY())  AS billiard_orders
      FROM sessions AS s
      LEFT JOIN (
        SELECT 
          foi.session_id,
          JSON_ARRAYAGG(
            JSON_OBJECT(
              'id',         foi.id,
              'food_order_id', foi.session_id,
              'food_id',    foi.food_id,
              'food_name',  f.name,
              'batch',      foi.batch,
              'quantity',   foi.quantity,
              'price_each', foi.price_each,
              'subtotal',   foi.subtotal
            )
          ) AS food_items
        FROM food_order_items AS foi
        LEFT JOIN foods AS f ON f.id = foi.food_id
        GROUP BY foi.session_id
      ) AS f ON f.session_id = s.id
      LEFT JOIN (
        SELECT
          bo.session_id,
          JSON_ARRAYAGG(
            JSON_OBJECT(
              'id',                      bo.id,
              'table_id',                bo.table_id,
              'table_number',            t.table_number,
              'session_id',              bo.session_id,
              'package_id',              bo.package_id,
              'duration',                bo.duration,
              'price',                   bo.price,
              'discount',                bo.discount,
              'status',                  bo.status,
              'type_package_price_id',   bo.type_package_price_id,
              'start_time',              DATE_FORMAT(bo.start_time, '%Y-%m-%dT%T'),
              'end_time',                DATE_FORMAT(bo.end_time,   '%Y-%m-%dT%T')
            )
          ) AS billiard_orders
        FROM billiard_orders AS bo
        JOIN billiard_tables AS t ON t.id = bo.table_id
        GROUP BY bo.session_id
      ) AS b ON b.session_id = s.id
      WHERE s.id = ?
      GROUP BY s.id
    `, [id]);

    if (!sessions) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // exactly the same post‐processing as `getAll`
    const s = sessions;
    const foodItems = Array.isArray(s.food_items)
      ? s.food_items
      : JSON.parse(s.food_items || '[]');
    const billiardOrders = Array.isArray(s.billiard_orders)
      ? s.billiard_orders
      : JSON.parse(s.billiard_orders || '[]');

    const now = new Date();
    const updatedBilliardOrders = billiardOrders.map(order => {
      const start = order.start_time ? new Date(order.start_time) : null;
      let end = order.end_time ? new Date(order.end_time) : null;
      if (start && end && end < start) end.setDate(end.getDate() + 1);
      const remaining = start && end && now >= start && now <= end
        ? Math.floor((end - now) / 1000)
        : 0;
      return { ...order, end_time: toWIBISOString(end), remaining_time_seconds: remaining };
    });

    res.json({
      ...s,
      food_items:  foodItems,
      billiard_orders: updatedBilliardOrders,
    });
  }
  catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
};


const create = async (req, res) => {
  const {
    guest_name,
    guest_phone,
    invoice_number,
    date,
    status,
    payment_method,
    discount_percent = 0,
    tax_rate = 0,
    ppn_rate         = 0,
    pay_amount = 0,
    food_items = [],
    billiard_orders = [],
  } = req.body;

  const conn = await db.getConnection();
  try {
    await conn.beginTransaction();

    // 1) compute aggregates
    const totalFood = food_items.reduce(
      (sum, f) => sum + f.quantity * f.price_each,
      0
    );
    const totalBilliard = billiard_orders.reduce((sum, b) => {
      const afterDisc = b.price * (1 - b.discount / 100);
      return sum + afterDisc;
    }, 0);

    // server‐side stored totals/amounts
    const discountAmt = totalFood + totalBilliard * (discount_percent / 100);
    const taxAmt = (totalFood + totalBilliard - discountAmt) * (tax_rate / 100);
    const grandTotal = totalFood + totalBilliard - discountAmt + taxAmt;

    // 2) decide paid_at
    // const paidAt =
    //   pay_amount >= grandTotal ? moment().format("YYYY-MM-DD HH:mm:ss") : null;

    // 2) decide paid_at dengan toleransi Rp 100
    const THRESHOLD = 100;
    const paidAt =
      pay_amount >= grandTotal - THRESHOLD
        ? moment().format("YYYY-MM-DD HH:mm:ss")
        : null;
    //const dbStatus = paidAt ? 'paid' : 'pending';

    // 3) insert session header
    const formattedDate = moment(date).format("YYYY-MM-DD HH:mm:ss");
    const [r] = await conn.query(
      `
      INSERT INTO sessions
        (guest_name, guest_phone, invoice_number,
         date, status, payment_method,
         total_food, total_billiard,
         discount_percent, tax_rate, ppn_rate, 
         pay_amount, paid_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `,
      [
        guest_name,
        guest_phone,
        invoice_number,
        formattedDate,
        status,
        payment_method,
        totalFood,
        totalBilliard,
        discount_percent,
        tax_rate,
        ppn_rate,
        pay_amount,
        paidAt,
      ]
    );
    const sessionId = r.insertId;

    // 4) insert food lines
    for (const f of food_items) {
      await conn.query(
        `
        INSERT INTO food_order_items
          (session_id, food_id, batch, quantity, price_each)
        VALUES (?, ?, ?, ?, ?)
      `,
        [sessionId, f.food_id, f.batch, f.quantity, f.price_each]
      );
    }

    // 5) insert billiard lines
    for (const b of billiard_orders) {
      await conn.query(
        `
        INSERT INTO billiard_orders
          (table_id, session_id, package_id,
           duration, price, discount, status,
           start_time, end_time, type_package_price_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          b.table_id,
          sessionId,
          b.package_id,
          b.duration,
          b.price,
          b.discount,
          b.status,
          b.start_time,
          b.end_time,
          b.type_package_price_id,
        ]
      );
    }

    await conn.commit();
    res.status(201).json({ id: sessionId });
  } catch (err) {
    await conn.rollback();
    console.error(err);
    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

const update = async (req, res) => {
  const { id } = req.params;
  const {
    guest_name,
    guest_phone,
    date,
    status,
    payment_method,
    discount_percent = 0,
    tax_rate = 0,
    ppn_rate         = 0,
    pay_amount = 0,
    food_items = [],
    billiard_orders = [],
  } = req.body;

  const conn = await db.getConnection();
  try {
    await conn.beginTransaction();

    // 1) compute aggregates
    const totalFood = food_items.reduce(
      (sum, f) => sum + f.quantity * f.price_each,
      0
    );
    const totalBilliard = billiard_orders.reduce((sum, b) => {
      const afterDisc = b.price * (1 - b.discount / 100);
      return sum + afterDisc;
    }, 0);

    // recalc discount/tax and grandTotal
    const discountAmt = totalFood + totalBilliard * (discount_percent / 100);
    const taxAmt = (totalFood + totalBilliard - discountAmt) * (tax_rate / 100);
    const grandTotal = totalFood + totalBilliard - discountAmt + taxAmt;

    // decide paid_at
    // const paidAt =
    //   pay_amount >= grandTotal ? moment().format("YYYY-MM-DD HH:mm:ss") : null;
    const THRESHOLD = 100;
    const paidAt =
      pay_amount >= grandTotal - THRESHOLD
        ? moment().format("YYYY-MM-DD HH:mm:ss")
        : null;

    // 2) update session header
    const formattedDate = moment(date).format("YYYY-MM-DD HH:mm:ss");
    const [r] = await conn.query(
      `
      UPDATE sessions
      SET
        guest_name       = ?,
        guest_phone      = ?,
        date             = ?,
        status           = ?,
        payment_method   = ?,
        total_food       = ?,
        total_billiard   = ?,
        discount_percent = ?,
        tax_rate         = ?,
        ppn_rate         = ?,
        pay_amount       = ?,
        paid_at          = ? 
      WHERE id = ?
    `,
      [
        guest_name,
        guest_phone,
        formattedDate,
        status,
        payment_method,
        totalFood,
        totalBilliard,
        discount_percent,
        tax_rate,
        ppn_rate,
        pay_amount,
        paidAt,
        id,
      ]
    );
    if (r.affectedRows === 0) {
      await conn.rollback();
      return res.status(404).json({ error: "Session not found" });
    }

    // 3) clear & re‐insert children
    await conn.query(`DELETE FROM food_order_items WHERE session_id = ?`, [id]);
    await conn.query(`DELETE FROM billiard_orders    WHERE session_id = ?`, [
      id,
    ]);

    for (const f of food_items) {
      await conn.query(
        `
        INSERT INTO food_order_items
          (session_id, food_id, batch, quantity, price_each)
        VALUES (?, ?, ?, ?, ?)
      `,
        [id, f.food_id, f.batch, f.quantity, f.price_each]
      );
    }
    for (const b of billiard_orders) {
      await conn.query(
        `
        INSERT INTO billiard_orders
          (table_id, session_id, package_id,
           duration, price, discount, status,
           start_time, end_time, type_package_price_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          b.table_id,
          id,
          b.package_id,
          b.duration,
          b.price,
          b.discount,
          b.status,
          b.start_time,
          b.end_time,
          b.type_package_price_id,
        ]
      );
    }

    await conn.commit();
    res.json({ success: true });
  } catch (err) {
    await conn.rollback();
    console.error(err);
    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

const remove = async (req, res) => {
  const { id } = req.params;
  const conn = await db.getConnection();
  try {
    await conn.beginTransaction();

    // 1) Cek printed_at
    const [[session]] = await conn.query(
      `SELECT printed_at FROM sessions WHERE id = ?`,
      [id]
    );
    if (session.printed_at) {
      await conn.rollback();
      return res.status(400).json({ error: "Cannot delete a printed session" });
    }

    // 2) Ambil semua meja + relay yang terkait session ini
    const [orders] = await conn.query(
      `
        SELECT
          bo.table_id,
          t.relay_id,
          t.table_number
        FROM billiard_orders bo
        JOIN billiard_tables t ON t.id = bo.table_id
        WHERE bo.session_id = ?
      `,
      [id]
    );

    // 3) Hapus data child
    await conn.query(`DELETE FROM food_order_items WHERE session_id = ?`, [id]);
    await conn.query(`DELETE FROM billiard_orders    WHERE session_id = ?`, [id]);

    // 4) Hapus session header
    const [r] = await conn.query(`DELETE FROM sessions WHERE id = ?`, [id]);
    if (r.affectedRows === 0) {
      await conn.rollback();
      return res.status(404).json({ error: "Session not found" });
    }

    // 5) Log “OFF” untuk tiap meja yang tadi aktif
    // for (const o of orders) {
    //   await insertRelayLog({
    //     orderId:     null,
    //     tableNumber: o.table_number,
    //     relayNumber: o.relay_id,
    //     action:      "OFF",
    //     status:      "SUCCESS"
    //   });
    // }

    await conn.commit();
    res.json({ success: true });
  } catch (err) {
    await conn.rollback();
    console.error(err);
    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

// const remove = async (req, res) => {
//   const { id } = req.params;
//   const conn = await db.getConnection();
//   try {
//     await conn.beginTransaction();

//     // 1) Cek printed_at
//     const [[session]] = await conn.query(
//       `SELECT printed_at FROM sessions WHERE id = ?`,
//       [id]
//     );
//     if (session.printed_at) {
//       await conn.rollback();
//       return res.status(400).json({ error: "Cannot delete a printed session" });
//     }

//     // 2) Ambil semua table_id terkait session ini
//     const [orders] = await conn.query(
//       `SELECT table_id FROM billiard_orders WHERE session_id = ?`,
//       [id]
//     );

//     // 3) Hapus data child
//     await conn.query(`DELETE FROM food_order_items   WHERE session_id = ?`, [id]);
//     await conn.query(`DELETE FROM billiard_orders     WHERE session_id = ?`, [id]);

//     // 4) Hapus session header
//     const [r] = await conn.query(`DELETE FROM sessions WHERE id = ?`, [id]);
//     if (r.affectedRows === 0) {
//       await conn.rollback();
//       return res.status(404).json({ error: "Session not found" });
//     }

//     // 5) Log “OFF” untuk tiap meja yang tadi aktif
//     const now = moment().format("YYYY-MM-DD HH:mm:ss");
//     for (const o of orders) {
//       await conn.query(
//         `INSERT INTO relay_logs (table_id, action, created_at)
//          VALUES (?, 'off', ?)`,
//         [o.table_id, now]
//       );
//     }

//     await conn.commit();
//     res.json({ success: true });
//   } catch (err) {
//     await conn.rollback();
//     console.error(err);
//     res.status(500).json({ error: err.message });
//   } finally {
//     conn.release();
//   }
// };


/**
 * POST /sessions/:id/print
 * Tandai session sebagai sudah diprint (printed_at = NOW())
 */
const markPrinted = async (req, res) => {
  const { id } = req.params;
  try {
    const [r] = await db.query(
      `UPDATE sessions
         SET printed_at = NOW()
       WHERE id = ?`,
      [id]
    );
    if (r.affectedRows === 0) {
      return res.status(404).json({ error: "Session not found" });
    }
    res.json({ success: true, printed_at: new Date().toISOString() });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
};

module.exports = {
  getAll,
  getById,
  create,
  update,
  remove,
  markPrinted,
};
