const db = require("../config/db");

exports.getTablesWithOrders = async (req, res) => {
  try {
    const [tables] = await db.query(`
      SELECT t.id, t.table_number, t.price, t.type, t.status,
        o.start_time, o.end_time
      FROM billiard_tables t
      LEFT JOIN billiard_orders o ON t.id = o.table_id AND o.status = 1
      ORDER BY t.table_number ASC
    `);

    const currentTime = new Date();

    const tablesWithCountdown = tables.map((table) => {
      let remaining_seconds = 0;
      if (table.status === 1 && table.end_time) {
        const endTime = new Date(table.end_time);
        remaining_seconds = Math.max(
          Math.floor((endTime - currentTime) / 1000),
          0
        );

        if (remaining_seconds <= 0) {
          table.status = 0;
        }
      }

      return {
        id: table.id,
        table_number: table.table_number,
        price: table.price,
        type: table.type,
        status: table.status,
        start_time: table.start_time,
        end_time: table.end_time,
        remaining_seconds,
      };
    });

    res.json(tablesWithCountdown);
  } catch (error) {
    console.error("Error fetching dashboard tables:", error);
    res.status(500).json({ error: "Failed to fetch dashboard tables" });
  }
};
