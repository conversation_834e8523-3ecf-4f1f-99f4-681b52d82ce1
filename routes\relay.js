const express = require("express");
const router = express.Router();
const relayController = require("../controllers/relayController");

router.get("/", relayController.getAllRelays);
router.get("/:id", relayController.getRelayById);
router.post("/", relayController.createRelay);
router.put("/:id", relayController.updateRelay);
router.delete("/:id", relayController.deleteRelay);
router.post("/:id/send", relayController.sendRelayCommand);

module.exports = router;
