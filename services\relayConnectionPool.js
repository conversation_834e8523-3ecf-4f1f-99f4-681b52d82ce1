// services/relayConnectionPool.js
const net = require('net');
const EventEmitter = require('events');

class RelayConnectionPool extends EventEmitter {
  constructor(options = {}) {
    super();
    this.pools = new Map(); // ip → { socket, busy, lastUsed }
    this.maxConnectionsPerIP = options.maxConnectionsPerIP || 2;
    this.connectionTimeout = options.connectionTimeout || 3000;
    this.idleTimeout = options.idleTimeout || 30000; // 30 detik idle
    this.retryDelay = options.retryDelay || 1000;
    
    // Cleanup idle connections setiap 10 detik
    this.cleanupInterval = setInterval(() => this.cleanup(), 10000);
  }

  async getConnection(ip) {
    // Cek pool yang ada
    if (!this.pools.has(ip)) {
      this.pools.set(ip, []);
    }
    
    const pool = this.pools.get(ip);
    
    // Cari connection yang idle
    for (const conn of pool) {
      if (!conn.busy && conn.socket && !conn.socket.destroyed) {
        conn.busy = true;
        conn.lastUsed = Date.now();
        return conn;
      }
    }
    
    // Buat connection baru jika belum mencapai limit
    if (pool.length < this.maxConnectionsPerIP) {
      const conn = await this.createConnection(ip);
      pool.push(conn);
      return conn;
    }
    
    // Tunggu connection tersedia
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timeout waiting for available connection'));
      }, this.connectionTimeout);
      
      const checkAvailable = setInterval(() => {
        for (const conn of pool) {
          if (!conn.busy && conn.socket && !conn.socket.destroyed) {
            clearInterval(checkAvailable);
            clearTimeout(timeout);
            conn.busy = true;
            conn.lastUsed = Date.now();
            resolve(conn);
            return;
          }
        }
      }, 100);
    });
  }

  async createConnection(ip) {
    return new Promise((resolve, reject) => {
      const socket = new net.Socket();
      const conn = {
        socket,
        ip,
        busy: true,
        lastUsed: Date.now(),
        created: Date.now()
      };
      
      socket.setKeepAlive(true, 5000);
      socket.setNoDelay(true);
      socket.setTimeout(this.connectionTimeout);
      
      socket.once('error', (err) => {
        console.error(`Connection error to ${ip}:`, err.message);
        conn.socket = null;
        reject(err);
      });
      
      socket.once('timeout', () => {
        socket.destroy();
        conn.socket = null;
        reject(new Error('Connection timeout'));
      });
      
      socket.once('close', () => {
        // Remove dari pool jika connection ditutup
        const pool = this.pools.get(ip) || [];
        const idx = pool.indexOf(conn);
        if (idx !== -1) pool.splice(idx, 1);
      });
      
      socket.connect(80, ip, () => {
        socket.setTimeout(0); // Disable timeout setelah connect
        resolve(conn);
      });
    });
  }

  releaseConnection(conn) {
    if (conn) {
      conn.busy = false;
      conn.lastUsed = Date.now();
    }
  }

  async sendCommand(ip, command) {
    let conn;
    let retries = 0;
    const maxRetries = 2;
    
    while (retries <= maxRetries) {
      try {
        conn = await this.getConnection(ip);
        
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Command send timeout'));
          }, 2000);
          
          conn.socket.write(command + '\n', (err) => {
            clearTimeout(timeout);
            this.releaseConnection(conn);
            
            if (err) {
              reject(err);
            } else {
              resolve({ success: true, command, to: ip });
            }
          });
        });
        
      } catch (err) {
        retries++;
        if (conn) {
          // Destroy connection yang error
          if (conn.socket) conn.socket.destroy();
          const pool = this.pools.get(ip) || [];
          const idx = pool.indexOf(conn);
          if (idx !== -1) pool.splice(idx, 1);
        }
        
        if (retries > maxRetries) {
          throw err;
        }
        
        // Delay sebelum retry
        await new Promise(r => setTimeout(r, this.retryDelay * retries));
      }
    }
  }

  cleanup() {
    const now = Date.now();
    
    for (const [ip, pool] of this.pools) {
      for (let i = pool.length - 1; i >= 0; i--) {
        const conn = pool[i];
        
        // Hapus connection yang idle terlalu lama
        if (!conn.busy && (now - conn.lastUsed) > this.idleTimeout) {
          if (conn.socket && !conn.socket.destroyed) {
            conn.socket.end();
          }
          pool.splice(i, 1);
        }
        
        // Hapus connection yang destroyed
        if (conn.socket && conn.socket.destroyed) {
          pool.splice(i, 1);
        }
      }
      
      // Hapus pool kosong
      if (pool.length === 0) {
        this.pools.delete(ip);
      }
    }
  }

  destroy() {
    clearInterval(this.cleanupInterval);
    
    // Close semua connections
    for (const [ip, pool] of this.pools) {
      for (const conn of pool) {
        if (conn.socket && !conn.socket.destroyed) {
          conn.socket.end();
        }
      }
    }
    
    this.pools.clear();
  }

  getStatus() {
    const status = {
      totalConnections: 0,
      activeConnections: 0,
      pools: {}
    };
    
    for (const [ip, pool] of this.pools) {
      const active = pool.filter(c => c.busy).length;
      status.pools[ip] = {
        total: pool.length,
        active,
        idle: pool.length - active
      };
      status.totalConnections += pool.length;
      status.activeConnections += active;
    }
    
    return status;
  }
}

module.exports = { RelayConnectionPool };