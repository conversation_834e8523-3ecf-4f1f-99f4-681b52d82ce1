// const { SerialPort } = require("serialport");

// const portPath = process.env.SERIAL_PORT_PATH || "COM9";
// const baudRate = process.env.SERIAL_BAUD_RATE || 9600;

// const serialPort = new SerialPort({
//   path: portPath,
//   baudRate: baudRate,
//   autoOpen: false,
// });

// serialPort.open((err) => {
//   if (err) {
//     return console.error("❌ Gagal membuka serial port:", err.message);
//   }
//   console.log(`✅ Serial port terbuka di ${portPath} @ ${baudRate} baud`);
// });

// serialPort.on("error", (err) => {
//   console.error("❌ Serial port error:", err.message);
// });

// module.exports = serialPort;
