// controllers/typePackagePriceController.js

const db = require("../config/db");

/**
 * <PERSON>k apakah sebuah `type_package_price` (oleh id) MASIH dipakai
 * di tabel `billiard_orders.type_package_price_id`.
 * 
 * @param {object} conn  Koneksi database (belum dijalankan commit/rollback)
 * @param {number} tierId
 * @returns {Promise<boolean>}  true jika masih direferensi, false jika tidak.
 */
async function _isTierReferenced(conn, tierId) {
  const [[{ cnt }]] = await conn.query(
    `SELECT COUNT(*) AS cnt
       FROM billiard_orders
      WHERE type_package_price_id = ?`,
    [tierId]
  );
  return cnt > 0;
}

/**
 * GET /api/type_package_prices
 * Query params: table_type, package_id
 */
// exports.getTypePackagePrices = async (req, res) => {
//   const { table_type, package_id } = req.query;
//   if (!table_type || !package_id) {
//     return res
//       .status(400)
//       .json({ message: "table_type and package_id are required" });
//   }

//   try {
//     const [rows] = await db.query(
//       `SELECT id, table_type, package_id, duration, price
//          FROM type_package_prices
//         WHERE table_type = ? AND package_id = ?
//         ORDER BY duration`,
//       [table_type, package_id]
//     );
//     res.json(rows);
//   } catch (err) {
//     console.error("Error fetching type_package_prices:", err);
//     res.status(500).json({ error: err.message });
//   }
// };

/**
 * GET /api/type_package_prices
 * Query params: table_type, package_id, [day_type]
 *
 * - If day_type is “weekday” or “weekend”, only that bucket is returned.
 * - Otherwise (no day_type or invalid), all rows for that table_type + package_id come back.
 */
exports.getTypePackagePrices = async (req, res) => {
  const { table_type, package_id, day_type: clientDay } = req.query;
  if (!table_type || !package_id) {
    return res
      .status(400)
      .json({ message: "table_type and package_id are required" });
  }

  // decide if we should filter by day_type
  const shouldFilter =
    clientDay === 'weekday' || clientDay === 'weekend';

  let sql, params;
  if (shouldFilter) {
    sql = `
      SELECT id, table_type, package_id, duration, price, day_type
        FROM type_package_prices
       WHERE table_type = ?
         AND package_id = ?
         AND day_type = ?
       ORDER BY duration
    `;
    params = [table_type, package_id, clientDay];
  } else {
    sql = `
      SELECT id, table_type, package_id, duration, price, day_type
        FROM type_package_prices
       WHERE table_type = ?
         AND package_id = ?
       ORDER BY day_type, duration
    `;
    params = [table_type, package_id];
  }

  try {
    const [rows] = await db.query(sql, params);
    res.json(rows);
  } catch (err) {
    console.error("Error fetching type_package_prices:", err);
    res.status(500).json({ error: err.message });
  }
};



// /**
//  * POST /api/type_package_prices
//  * Body: { table_type, package_id, tiers: [ { id?, duration, price }, … ] }
//  * 
//  * - Jika `tiers[i].id` ada, lakukan UPDATE pada row tsb.
//  * - Jika `tiers[i].id` tidak ada, lakukan INSERT baru.
//  * - Setelah itu, hapus baris lama (id yang tidak muncul dalam payload) hanya jika
//  *   baris tersebut TIDAK direferensi oleh billiard_orders.
//  */
// exports.upsertTypePackagePrices = async (req, res) => {
//   const { table_type, package_id, tiers } = req.body;
//   if (!table_type || !package_id || !Array.isArray(tiers)) {
//     return res
//       .status(400)
//       .json({ message: "table_type, package_id and tiers[] are required" });
//   }

//   const conn = await db.getConnection();
//   try {
//     await conn.beginTransaction();

//     // 1) Ambil daftar ID lama untuk kombinasi (table_type, package_id)
//     const [existingRows] = await conn.query(
//       `SELECT id
//          FROM type_package_prices
//         WHERE table_type = ? AND package_id = ?`,
//       [table_type, package_id]
//     );
//     const existingIds = existingRows.map((r) => r.id);

//     // 2) Proses setiap tier di payload:
//     //    - Jika tier.id ada, maka UPDATE row tsb.
//     //    - Jika tier.id tidak ada, maka INSERT row baru.
//     //    Kumpulkan semua ID yang berhasil di‐UPDATE atau INSERT.
//     const processedIds = [];

//     for (const tier of tiers) {
//       // Pastikan minimal memiliki duration dan price
//       if (typeof tier.duration !== "number" || typeof tier.price !== "number") {
//         throw new Error(
//           "Setiap tier harus memiliki `duration` (int) dan `price` (number)."
//         );
//       }

//       if (tier.id) {
//         // >> UPDATE baris yang sudah ada
//         processedIds.push(tier.id);
//         await conn.query(
//           `UPDATE type_package_prices
//              SET duration = ?, price = ?
//            WHERE id = ?`,
//           [tier.duration, tier.price, tier.id]
//         );
//       } else {
//         // >> INSERT baris baru
//         const [insertRes] = await conn.query(
//           `INSERT INTO type_package_prices
//              (table_type, package_id, duration, price)
//            VALUES (?, ?, ?, ?)`,
//           [table_type, package_id, tier.duration, tier.price]
//         );
//         processedIds.push(insertRes.insertId);
//       }
//     }

//     // 3) Temukan ID lama yang **tidak** ada di dalam processedIds
//     const obsoleteIds = existingIds.filter((oldId) => !processedIds.includes(oldId));

//     // 4) Untuk setiap obsoleteId, hanya DELETE jika **tidak** direferensi di billiard_orders
//     for (const oldId of obsoleteIds) {
//       const isReferenced = await _isTierReferenced(conn, oldId);
//       if (isReferenced) {
//         console.warn(
//           `[WARN] Tier id=${oldId} masih dipakai di billiard_orders → SKIP DELETE`
//         );
//         continue;
//       }
//       await conn.query(
//         `DELETE FROM type_package_prices WHERE id = ?`,
//         [oldId]
//       );
//     }

//     await conn.commit();
//     return res
//       .status(200)
//       .json({ message: "Type package prices successfully upserted." });
//   } catch (err) {
//     await conn.rollback();
//     console.error("Error upserting type_package_prices:", err);
//     return res.status(500).json({ error: err.message });
//   } finally {
//     conn.release();
//   }
// };

/**
 * POST /api/type_package_prices
 * Body: { table_type, package_id, tiers: [ { id?, duration, price, optional day_type }, … ] }
 */
exports.upsertTypePackagePrices = async (req, res) => {
  const { table_type, package_id, tiers } = req.body;
  if (!table_type || !package_id || !Array.isArray(tiers)) {
    return res
      .status(400)
      .json({ message: "table_type, package_id and tiers[] are required" });
  }

  const conn = await db.getConnection();
  try {
    await conn.beginTransaction();

    // 1) Ambil semua ID existing untuk kombinasi table_type+package_id
    const [existingRows] = await conn.query(
      `SELECT id
         FROM type_package_prices
        WHERE table_type = ? AND package_id = ?`,
      [table_type, package_id]
    );
    const existingIds = existingRows.map((r) => r.id);

    // 2) Proses setiap tier
    const processedIds = [];
    const todayDow = new Date().getDay();
    const defaultDay = [0, 6].includes(todayDow) ? "weekend" : "weekday";

    for (const tier of tiers) {
      // validation
      if (
        typeof tier.duration !== "number" ||
        typeof tier.price !== "number"
      ) {
        throw new Error(
          "Each tier must have numerical `duration` and `price`."
        );
      }

      // determine this tier’s day_type
      const tierDay =
        tier.day_type === "weekday" || tier.day_type === "weekend"
          ? tier.day_type
          : defaultDay;

      if (tier.id) {
        // update existing
        processedIds.push(tier.id);
        await conn.query(
          `UPDATE type_package_prices
              SET duration = ?, price = ?, day_type = ?
            WHERE id = ?`,
          [tier.duration, tier.price, tierDay, tier.id]
        );
      } else {
        // insert new
        const [insertRes] = await conn.query(
          `INSERT INTO type_package_prices
             (table_type, package_id, duration, price, day_type)
           VALUES (?, ?, ?, ?, ?)`,
          [table_type, package_id, tier.duration, tier.price, tierDay]
        );
        processedIds.push(insertRes.insertId);
      }
    }

    // 3) Any old IDs not in processedIds should be removed—if not referenced
    const obsoleteIds = existingIds.filter(
      (oldId) => !processedIds.includes(oldId)
    );
    for (const oldId of obsoleteIds) {
      const inUse = await _isTierReferenced(conn, oldId);
      if (inUse) {
        console.warn(
          `[WARN] Tier id=${oldId} still in use → skipping delete`
        );
        continue;
      }
      await conn.query(
        `DELETE FROM type_package_prices WHERE id = ?`,
        [oldId]
      );
    }

    await conn.commit();
    return res
      .status(200)
      .json({ message: "Type package prices successfully upserted." });
  } catch (err) {
    await conn.rollback();
    console.error("Error upserting type_package_prices:", err);
    return res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};
