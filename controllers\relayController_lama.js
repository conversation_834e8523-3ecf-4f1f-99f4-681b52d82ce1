// const db = require('../config/db');
// const net = require("net");
// const { relayQueue } = require('../services/relayQueue');

// let relayStateCache = {}; // untuk tracking status terakhir yg sudah dikirim ke relay
// // Connection pool untuk mengurangi overhead socket creation
// const connectionPool = new Map();
// const MAX_POOL_SIZE = 32; // Pool untuk 64 relay
// const CONNECTION_TIMEOUT = 2500; // Further reduced timeout untuk 64 relay

// /**
//  * ✅ Get All Relays
//  * @route GET /api/relays
//  */
// exports.getAllRelays = async (req, res) => {
//   try {
//     const [relays] = await db.query(`
//       SELECT id, relay_name, status, ip_address, hardware_id 
//       FROM relay
//       ORDER BY id ASC
//     `);
//     res.json(relays);
//   } catch (error) {
//     console.error("Error fetching relays:", error);
//     res.status(500).json({ error: "Gagal mengambil data relay" });
//   }
// };

// /**
//  * ✅ Get Relay by ID
//  * @route GET /api/relays/:id
//  */
// exports.getRelayById = async (req, res) => {
//   try {
//     const [relay] = await db.query(
//       "SELECT id, relay_name, status, ip_address FROM relay WHERE id = ?",
//       [req.params.id]
//     );
//     if (relay.length === 0) {
//       return res.status(404).json({ error: "Relay tidak ditemukan" });
//     }
//     res.json(relay[0]);
//   } catch (error) {
//     console.error("Error fetching relay:", error);
//     res.status(500).json({ error: "Gagal mengambil data relay" });
//   }
// };

// /**
//  * ✅ Create Relay
//  * @route POST /api/relays
//  */
// exports.createRelay = async (req, res) => {
//   try {
//     const { relay_name, status, ip_address } = req.body;
//     if (!relay_name || !ip_address) {
//       return res
//         .status(400)
//         .json({ error: "Nama relay dan IP address wajib diisi" });
//     }
//     const [result] = await db.query(
//       `INSERT INTO relay (relay_name, status, ip_address, created_at, updated_at)
//        VALUES (?, ?, ?, NOW(), NOW())`,
//       [relay_name, status ?? 0, ip_address]
//     );
//     res
//       .status(201)
//       .json({ id: result.insertId, message: "Relay berhasil ditambahkan" });
//   } catch (error) {
//     console.error("Error creating relay:", error);
//     res.status(500).json({ error: "Gagal menambahkan relay" });
//   }
// };

// /**
//  * ✅ Update Relay
//  * @route PUT /api/relays/:id
//  */
// exports.updateRelay = async (req, res) => {
//   try {
//     const { relay_name, status, ip_address } = req.body;
//     if (!relay_name || !ip_address) {
//       return res
//         .status(400)
//         .json({ error: "Nama relay dan IP address wajib diisi" });
//     }
//     const [result] = await db.query(
//       `UPDATE relay 
//          SET relay_name = ?, status = ?, ip_address = ?, updated_at = NOW()
//        WHERE id = ?`,
//       [relay_name, status ?? 0, ip_address, req.params.id]
//     );
//     if (result.affectedRows === 0) {
//       return res.status(404).json({ error: "Relay tidak ditemukan" });
//     }
//     res.json({ message: "Relay berhasil diperbarui" });
//   } catch (error) {
//     console.error("Error updating relay:", error);
//     res.status(500).json({ error: "Gagal memperbarui relay" });
//   }
// };

// /**
//  * ✅ Delete Relay
//  * @route DELETE /api/relays/:id
//  */
// exports.deleteRelay = async (req, res) => {
//   try {
//     const [result] = await db.query(
//       "DELETE FROM relay WHERE id = ?",
//       [req.params.id]
//     );
//     if (result.affectedRows === 0) {
//       return res.status(404).json({ error: "Relay tidak ditemukan" });
//     }
//     res.json({ message: "Relay berhasil dihapus" });
//   } catch (error) {
//     console.error("Error deleting relay:", error);
//     res.status(500).json({ error: "Gagal menghapus relay" });
//   }
// };

// /**
//  * 🔁 Send ON/OFF Command to Relay
//  * @route POST /api/relays/:id/send
//  * body: { action: "on" | "off" }
//  */
// exports.sendRelayCommand = async (req, res) => {
//   const { id } = req.params;
//   const { action } = req.body;
//   if (!["on", "off"].includes(action)) {
//     return res.status(400).json({ error: "Aksi harus 'on' atau 'off'" });
//   }
//   try {
//     const result = await sendRelayCommandHelper(id, action);
//     if (!result.success) {
//       return res.status(504).json({ error: `Relay error: ${result.error}` });
//     }
//     res.json(result);
//   } catch (err) {
//     console.error("Unexpected error:", err);
//     res.status(500).json({ error: err.message });
//   }
// };

// /**
//  * Helper: kirim perintah ON/OFF ke relay oleh ID dengan connection pooling.
//  * Command: "n<id>" untuk ON, "f<id>" untuk OFF.
//  */
// async function sendRelayCommandHelper(relayId, action, useQueue = true) {
//   // Jika menggunakan queue system (default untuk mencegah interferensi)
//   if (useQueue) {
//     const priority = action === "off" ? 1 : 0; // OFF command priority lebih tinggi
//     return new Promise((resolve, reject) => {
//       const commandObj = relayQueue.enqueue(relayId, action, priority);
      
//       // Listen untuk hasil dari queue
//       const timeout = setTimeout(() => {
//         reject(new Error("Queue timeout"));
//       }, 10000);
      
//       relayQueue.once(`completed_${relayId}_${commandObj.timestamp}`, (result) => {
//         clearTimeout(timeout);
//         resolve(result);
//       });
      
//       relayQueue.once(`failed_${relayId}_${commandObj.timestamp}`, (error) => {
//         clearTimeout(timeout);
//         reject(error);
//       });
//     });
//   }

//   // Direct execution (untuk emergency atau testing)
//   return await sendRelayCommandDirect(relayId, action);
// }

// async function sendRelayCommandDirect(relayId, action) {
//   const [rows] = await db.query(
//     "SELECT ip_address, relay_name FROM relay WHERE id = ?",
//     [relayId]
//   );
//   if (!rows.length) throw new Error("Relay tidak ditemukan");
//   const { ip_address, relay_name } = rows[0];
  
//   if (!ip_address || ip_address.trim() === '') {
//     throw new Error(`Relay ${relay_name} tidak memiliki IP address`);
//   }
  
//   const command = action === "on" ? `n${relayId}` : `f${relayId}`;

//   // Don't use connection pool for now - create fresh connections
//   return new Promise((resolve) => {
//     const socket = new net.Socket();
//     const PORT = 80;
//     let done = false;
//     let responseReceived = false;

//     function finish(result) {
//       if (done) return;
//       done = true;
      
//       // Always destroy socket after use
//       if (!socket.destroyed) {
//         socket.destroy();
//       }
      
//       resolve(result);
//     }

//     // Set socket options for better reliability
//     socket.setKeepAlive(false);
//     socket.setNoDelay(true);
//     socket.setTimeout(CONNECTION_TIMEOUT);

//     socket.on("connect", () => {
//       console.log(`🔗 [RELAY] Connected to ${relay_name} (${ip_address})`);
      
//       socket.write(command + "\n", (err) => {
//         if (err) {
//           console.error(`❌ [RELAY] Write error to ${relay_name}:`, err.message);
//           return finish({ success: false, error: `Write error: ${err.message}` });
//         }
        
//         console.log(`📤 [RELAY] Command sent to ${relay_name}: ${command}`);
        
//         // Wait a bit for potential response, then consider success
//         setTimeout(() => {
//           if (!done) {
//             finish({ success: true, command, to: ip_address, relay_name });
//           }
//         }, 100);
//       });
//     });

//     socket.on("data", (data) => {
//       responseReceived = true;
//       console.log(`📥 [RELAY] Response from ${relay_name}:`, data.toString().trim());
//     });

//     socket.on("error", (err) => {
//       console.error(`❌ [RELAY] Connection error to ${relay_name} (${ip_address}):`, err.message);
//       finish({ 
//         success: false, 
//         error: `Connection error: ${err.message}`,
//         relay_name,
//         ip_address 
//       });
//     });

//     socket.on("timeout", () => {
//       console.warn(`⏰ [RELAY] Timeout connecting to ${relay_name} (${ip_address})`);
//       finish({ 
//         success: false, 
//         error: "Connection timeout",
//         relay_name,
//         ip_address 
//       });
//     });

//     socket.on("close", () => {
//       console.log(`🔌 [RELAY] Connection closed to ${relay_name}`);
//     });

//     console.log(`🔄 [RELAY] Attempting to connect to ${relay_name} (${ip_address}:${PORT})`);
//     socket.connect(PORT, ip_address);
//   });
// }

// async function sendCommandThroughSocket(socket, command, ip_address) {
//   return new Promise((resolve) => {
//     socket.write(command + "\n", (err) => {
//       if (err) {
//         resolve({ success: false, error: err.message });
//       } else {
//         resolve({ success: true, command, to: ip_address });
//       }
//     });
//   });
// }

// // Cleanup connection pool periodically
// setInterval(() => {
//   for (const [key, socket] of connectionPool) {
//     if (socket.destroyed || socket.readyState !== 'open') {
//       connectionPool.delete(key);
//     }
//   }
// }, 60000); // Cleanup every minute

// module.exports.sendRelayCommandHelper = sendRelayCommandHelper;

// // ======================================================
// // 🔁 CRON: Sinkronisasi relay ke status yang benar tiap 5 detik
// // ======================================================

// // OPTIMIZED SYNC - Batch processing untuk 32 relay
// async function autoSyncRelaysEvery10Seconds() {
//   try {
//     const [relays] = await db.query(`SELECT id, status FROM relay ORDER BY id`);
//     console.log(`🔄 [SYNC] Starting batch sync for ${relays.length} relays`);
    
//     // Batch relays into groups of 12 untuk 64 relay
//     const batchSize = 12;
//     const batches = [];
//     for (let i = 0; i < relays.length; i += batchSize) {
//       batches.push(relays.slice(i, i + batchSize));
//     }

//     // Process batches with delay
//     for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
//       const batch = batches[batchIndex];
//       console.log(`🔄 [SYNC] Processing batch ${batchIndex + 1}/${batches.length}`);
      
//       const promises = batch.map(async (relay) => {
//         const expected = relay.status === 1 ? "on" : "off";
//         try {
//           const result = await sendRelayCommandHelper(relay.id, expected, false); // Direct untuk sync
//           if (result.success) {
//             console.log(`✅ [SYNC] Relay ${relay.id} -> ${expected.toUpperCase()}`);
//           } else {
//             console.warn(`⚠️ [SYNC] Relay ${relay.id} failed: ${result.error}`);
//           }
//           return { relayId: relay.id, success: result.success };
//         } catch (error) {
//           console.error(`❌ [SYNC] Relay ${relay.id} error:`, error.message);
//           return { relayId: relay.id, success: false };
//         }
//       });

//       await Promise.allSettled(promises);
      
//       // Delay between batches untuk menghindari network flooding
//       if (batchIndex < batches.length - 1) {
//         await new Promise(resolve => setTimeout(resolve, 500));
//       }
//     }
    
//     console.log(`✅ [SYNC] Batch sync completed`);
//   } catch (error) {
//     console.error("❌ Error di autoSyncRelaysEvery10Seconds:", error);
//   }
// }

// // Increased interval untuk mengurangi network load dengan 64 relay
// setInterval(autoSyncRelaysEvery10Seconds, 20000); // 20 detik untuk 64 relay