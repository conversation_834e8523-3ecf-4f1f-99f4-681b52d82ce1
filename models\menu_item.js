"use strict";
module.exports = (sequelize, DataTypes) => {
  const MenuItem = sequelize.define(
    "MenuItem",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      description: DataTypes.TEXT,
      price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
      is_available: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
    },
    {
      tableName: "menu_items",
      underscored: true,
      timestamps: true,
    }
  );

  // Associations (if you need them later):
  // MenuItem.associate = models => {
  //   MenuItem.hasMany(models.FoodOrderItem, { foreignKey: 'menu_item_id' });
  // };

  return MenuItem;
};
