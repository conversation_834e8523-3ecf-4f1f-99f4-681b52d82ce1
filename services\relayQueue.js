// // services/relayQueue.js

// const EventEmitter                  = require('events');
// const { sendRelayCommandDirect }    = require('./relayTransport');

// class RelayCommandQueue extends EventEmitter {
//   constructor() {
//     super();
//     // Map<relayId, Array<CommandObj>>
//     this.queues = new Map();
//     // Set<relayId> sedang diproses
//     this.processing = new Set();
//     this.maxConcurrent = 12;    // batas paralelisme
//     this.currentProcessing = 0;
//   }

//   /**
//    * Tambah command ke antrian untuk relay tertentu
//    * @param {number} relayId 
//    * @param {'on'|'off'} command 
//    * @param {number} priority  higher value → dikerjakan lebih dulu
//    */
//   // enqueue(relayId, command, priority = 0) {
//   //   if (!this.queues.has(relayId)) {
//   //     this.queues.set(relayId, []);
//   //   }
//   //   const cmdObj = {
//   //     relayId,
//   //     command,
//   //     priority,
//   //     timestamp: Date.now(),
//   //     retries: 0,
//   //     maxRetries: 2
//   //   };
//   //   const q = this.queues.get(relayId);
//   //   // sisipkan sesuai priority (descending)
//   //   const idx = q.findIndex(item => item.priority < priority);
//   //   if (idx === -1) q.push(cmdObj);
//   //   else           q.splice(idx, 0, cmdObj);

//   //   // coba proses jika slot tersedia
//   //   this.processNext();
//   //   return cmdObj;
//   // }

//   enqueue(relayId, command, priority = 0) {
//   if (!this.queues.has(relayId)) this.queues.set(relayId, []);

//   // buat Promise untuk external chaining
//   let resolveFn, rejectFn;
//   const promise = new Promise((resolve, reject) => {
//     resolveFn = resolve;
//     rejectFn  = reject;
//   });

//   const cmdObj = {
//     relayId,
//     command,
//     priority,
//     timestamp: Date.now(),
//     retries: 0,
//     maxRetries: 2,
//     // simpan resolver/rejector supaya bisa dipanggil di executeCommand
//     _resolve: resolveFn,
//     _reject:  rejectFn
//   };

//   // sisipkan berdasar priority
//   const q = this.queues.get(relayId);
//   const idx = q.findIndex(item => item.priority < priority);
//   if (idx === -1) q.push(cmdObj);
//   else           q.splice(idx, 0, cmdObj);

//   this.processNext();
//   return promise;  // ← sekarang caller mendapatkan Promise
// }


//   /** Cari dan jalankan satu perintah berikutnya bila ada slot */
//   async processNext() {
//     if (this.currentProcessing >= this.maxConcurrent) return;

//     let nextCmd, relayId;
//     for (const [rid, q] of this.queues) {
//       if (!this.processing.has(rid) && q.length > 0) {
//         relayId = rid;
//         nextCmd = q.shift();
//         break;
//       }
//     }
//     if (!nextCmd) return;

//     this.processing.add(relayId);
//     this.currentProcessing++;

//     try {
//       await this.executeCommand(nextCmd);
//     } catch (err) {
//       console.error(`❌ [QUEUE] Error executing relay ${relayId}:`, err.message);
//       // retry jika masih boleh
//       if (nextCmd.retries < nextCmd.maxRetries) {
//         nextCmd.retries++;
//         this.queues.get(relayId).unshift(nextCmd);
//       }
//     } finally {
//       this.processing.delete(relayId);
//       this.currentProcessing--;
//       if (this.queues.get(relayId)?.length === 0) {
//         this.queues.delete(relayId);
//       }
//       // jadwalkan proses berikutnya
//       setImmediate(() => this.processNext());
//     }
//   }

//   /**
//    * Eksekusi satu commandObj langsung lewat transport layer
//    * @param {Object} cmdObj 
//    */
//   // async executeCommand(cmdObj) {
//   //   console.log(`🔄 [QUEUE] relay ${cmdObj.relayId}: ${cmdObj.command.toUpperCase()}`);
//   //   const start = Date.now();
//   //   // panggil direct TCP send
//   //   const result = await sendRelayCommandDirect(cmdObj.relayId, cmdObj.command);
//   //   if (result.success) {
//   //     console.log(`✅ [QUEUE] relay ${cmdObj.relayId} ${cmdObj.command.toUpperCase()} done in ${Date.now() - start}ms`);
//   //     this.emit(`completed_${cmdObj.relayId}_${cmdObj.timestamp}`, result);
//   //   } else {
//   //     throw new Error(result.error || 'Unknown transport error');
//   //   }
//   // }

//   async executeCommand(cmdObj) {
//   try {
//     const result = await sendRelayCommandDirect(cmdObj.relayId, cmdObj.command);
//     if (result.success) {
//       // … logging …
//       cmdObj._resolve(result);
//     } else {
//       throw new Error(result.error || 'Unknown error');
//     }
//   } catch (err) {
//     // … retry logic …
//     if (cmdObj.retries >= cmdObj.maxRetries) {
//       cmdObj._reject(err);
//     } else {
//       // enqueue ulang untuk retry
//       cmdObj.retries++;
//       this.queues.get(cmdObj.relayId).unshift(cmdObj);
//     }
//   }
// }


//   /** Untuk monitoring: berapa total queued, processing, dan per-relay */
//   getStatus() {
//     const status = {
//       totalQueued: 0,
//       processing: this.processing.size,
//       maxConcurrent: this.maxConcurrent,
//       queues: {}
//     };
//     for (const [rid, q] of this.queues) {
//       status.totalQueued += q.length;
//       status.queues[rid] = q.length;
//     }
//     return status;
//   }

//   /** Kosongkan semua command yang masih antre untuk satu relay */
//   clearQueue(relayId) {
//     this.queues.delete(relayId);
//     console.log(`🗑 [QUEUE] Cleared queue for relay ${relayId}`);
//   }
// }

// // single shared instance
// const relayQueue = new RelayCommandQueue();

// module.exports = { relayQueue };

// services/relayQueue.js - Versi Optimized

// const EventEmitter = require('events');
// const { sendRelayCommandDirect } = require('./relayTransport');

// class RelayCommandQueue extends EventEmitter {
//   constructor() {
//     super();
//     this.queues = new Map();
//     this.processing = new Set();
//     this.maxConcurrent = 8;        // Turunkan dari 12 ke 8
//     this.currentProcessing = 0;
    
//     // Rate limiting
//     this.commandsSentLastMinute = [];
//     this.maxCommandsPerMinute = 100;  // Maksimal 100 command per menit
    
//     // Backoff strategy
//     this.backoffDelays = new Map(); // relayId → nextDelay
//     this.baseBackoffDelay = 500;    // 500ms base delay
//     this.maxBackoffDelay = 5000;    // Max 5 detik
    
//     // Monitor rate limit setiap detik
//     this.rateLimitInterval = setInterval(() => this.cleanupRateLimit(), 1000);
//   }

//   enqueue(relayId, command, priority = 0) {
//     // Check rate limit
//     if (this.isRateLimited()) {
//       const err = new Error('Rate limit exceeded');
//       return Promise.reject(err);
//     }
    
//     if (!this.queues.has(relayId)) {
//       this.queues.set(relayId, []);
//     }

//     let resolveFn, rejectFn;
//     const promise = new Promise((resolve, reject) => {
//       resolveFn = resolve;
//       rejectFn  = reject;
//     });

//     const cmdObj = {
//       relayId,
//       command,
//       priority,
//       timestamp: Date.now(),
//       retries: 0,
//       maxRetries: 2,
//       _resolve: resolveFn,
//       _reject:  rejectFn
//     };

//     // Insert berdasarkan priority
//     const q = this.queues.get(relayId);
    
//     // Check queue depth
//     if (q.length >= 10) {
//       console.warn(`⚠️ Queue for relay ${relayId} is full (${q.length}), dropping oldest`);
//       const dropped = q.shift();
//       if (dropped._reject) {
//         dropped._reject(new Error('Queue overflow'));
//       }
//     }
    
//     const idx = q.findIndex(item => item.priority < priority);
//     if (idx === -1) q.push(cmdObj);
//     else q.splice(idx, 0, cmdObj);

//     // Process dengan delay jika ada backoff
//     const backoffDelay = this.backoffDelays.get(relayId) || 0;
//     if (backoffDelay > 0) {
//       setTimeout(() => this.processNext(), backoffDelay);
//     } else {
//       setImmediate(() => this.processNext());
//     }
    
//     return promise;
//   }

//   async processNext() {
//     if (this.currentProcessing >= this.maxConcurrent) return;
    
//     // Check rate limit
//     if (this.isRateLimited()) {
//       console.warn('⚠️ Rate limit reached, pausing queue processing');
//       setTimeout(() => this.processNext(), 1000);
//       return;
//     }

//     let nextCmd, relayId;
    
//     // Find relay with highest priority command
//     let highestPriority = -1;
    
//     for (const [rid, q] of this.queues) {
//       if (!this.processing.has(rid) && q.length > 0) {
//         const cmd = q[0]; // Peek at first command
//         if (cmd.priority > highestPriority) {
//           highestPriority = cmd.priority;
//           relayId = rid;
//           nextCmd = cmd;
//         }
//       }
//     }
    
//     if (!nextCmd) return;
    
//     // Remove from queue
//     this.queues.get(relayId).shift();
    
//     this.processing.add(relayId);
//     this.currentProcessing++;
    
//     // Track for rate limiting
//     this.commandsSentLastMinute.push(Date.now());

//     try {
//       await this.executeCommand(nextCmd);
      
//       // Reset backoff on success
//       this.backoffDelays.delete(relayId);
      
//     } catch (err) {
//       console.error(`❌ [QUEUE] Error executing relay ${relayId}:`, err.message);
      
//       // Handle retry with backoff
//       if (nextCmd.retries < nextCmd.maxRetries) {
//         nextCmd.retries++;
        
//         // Calculate backoff delay
//         const currentBackoff = this.backoffDelays.get(relayId) || this.baseBackoffDelay;
//         const nextBackoff = Math.min(currentBackoff * 2, this.maxBackoffDelay);
//         this.backoffDelays.set(relayId, nextBackoff);
        
//         console.log(`🔄 Retry ${nextCmd.retries}/${nextCmd.maxRetries} for relay ${relayId} after ${nextBackoff}ms`);
        
//         // Re-insert with same priority
//         this.queues.get(relayId).unshift(nextCmd);
//       } else {
//         // Max retries reached
//         if (nextCmd._reject) {
//           nextCmd._reject(err);
//         }
//       }
//     } finally {
//       this.processing.delete(relayId);
//       this.currentProcessing--;
      
//       // Cleanup empty queues
//       if (this.queues.get(relayId)?.length === 0) {
//         this.queues.delete(relayId);
//       }
      
//       // Schedule next with rate limit consideration
//       const delay = this.isNearRateLimit() ? 100 : 10;
//       setTimeout(() => this.processNext(), delay);
//     }
//   }

//   async executeCommand(cmdObj) {
//     const start = Date.now();
//     console.log(`🔄 [QUEUE] relay ${cmdObj.relayId}: ${cmdObj.command.toUpperCase()}`);
    
//     try {
//       const result = await sendRelayCommandDirect(cmdObj.relayId, cmdObj.command);
      
//       const duration = Date.now() - start;
//       console.log(`✅ [QUEUE] relay ${cmdObj.relayId} ${cmdObj.command.toUpperCase()} done in ${duration}ms`);
      
//       if (cmdObj._resolve) {
//         cmdObj._resolve(result);
//       }
      
//       this.emit(`completed_${cmdObj.relayId}_${cmdObj.timestamp}`, result);
      
//     } catch (err) {
//       // Log error details
//       console.error(`❌ [QUEUE] Command failed for relay ${cmdObj.relayId}:`, {
//         command: cmdObj.command,
//         error: err.message,
//         retries: cmdObj.retries
//       });
      
//       throw err;
//     }
//   }

//   isRateLimited() {
//     this.cleanupRateLimit();
//     return this.commandsSentLastMinute.length >= this.maxCommandsPerMinute;
//   }

//   isNearRateLimit() {
//     this.cleanupRateLimit();
//     return this.commandsSentLastMinute.length >= (this.maxCommandsPerMinute * 0.8);
//   }

//   cleanupRateLimit() {
//     const oneMinuteAgo = Date.now() - 60000;
//     this.commandsSentLastMinute = this.commandsSentLastMinute.filter(
//       timestamp => timestamp > oneMinuteAgo
//     );
//   }

//   getStatus() {
//     this.cleanupRateLimit();
    
//     const status = {
//       totalQueued: 0,
//       processing: this.processing.size,
//       currentProcessing: this.currentProcessing,
//       maxConcurrent: this.maxConcurrent,
//       commandsLastMinute: this.commandsSentLastMinute.length,
//       maxCommandsPerMinute: this.maxCommandsPerMinute,
//       rateLimited: this.isRateLimited(),
//       queues: {},
//       backoffs: {}
//     };
    
//     for (const [rid, q] of this.queues) {
//       status.totalQueued += q.length;
//       status.queues[rid] = q.length;
//     }
    
//     for (const [rid, delay] of this.backoffDelays) {
//       status.backoffs[rid] = delay;
//     }
    
//     return status;
//   }

//   clearQueue(relayId) {
//     const queue = this.queues.get(relayId);
//     if (queue) {
//       // Reject all pending commands
//       for (const cmd of queue) {
//         if (cmd._reject) {
//           cmd._reject(new Error('Queue cleared'));
//         }
//       }
//       this.queues.delete(relayId);
//       console.log(`🗑 [QUEUE] Cleared queue for relay ${relayId}`);
//     }
//   }

//   destroy() {
//     clearInterval(this.rateLimitInterval);
    
//     // Reject all pending commands
//     for (const [relayId, queue] of this.queues) {
//       for (const cmd of queue) {
//         if (cmd._reject) {
//           cmd._reject(new Error('Queue destroyed'));
//         }
//       }
//     }
    
//     this.queues.clear();
//     this.processing.clear();
//     this.backoffDelays.clear();
//     this.commandsSentLastMinute = [];
//   }
// }

// // Single shared instance
// const relayQueue = new RelayCommandQueue();

// // Graceful shutdown
// process.on('SIGINT', () => {
//   console.log('🛑 Shutting down relay queue...');
//   relayQueue.destroy();
// });

// process.on('SIGTERM', () => {
//   console.log('🛑 Shutting down relay queue...');
//   relayQueue.destroy();
// });

// // Export
// module.exports = { relayQueue };

// services/relayQueue.js - Enhanced Version with Better Rate Limiting

const EventEmitter = require('events');
const { sendRelayCommandDirect } = require('./relayTransport');

class RelayCommandQueue extends EventEmitter {
  constructor() {
    super();
    this.queues = new Map();
    this.processing = new Set();
    this.maxConcurrent = 6;        // Reduced from 8 to 6
    this.currentProcessing = 0;
    
    // Enhanced rate limiting with multiple tiers
    this.commandsSentLastMinute = [];
    this.commandsSentLast10Seconds = [];
    this.maxCommandsPerMinute = 80;    // Reduced from 100
    this.maxCommandsPer10Seconds = 15; // New: burst protection
    
    // Enhanced backoff strategy
    this.backoffDelays = new Map();
    this.baseBackoffDelay = 300;       // Reduced from 500ms
    this.maxBackoffDelay = 8000;       // Increased max
    this.backoffMultiplier = 1.5;      // Gentler exponential backoff
    
    // IP-based rate limiting
    this.ipCommandTracking = new Map(); // ip -> timestamps[]
    this.maxCommandsPerIPPerMinute = 25;
    
    // Command deduplication
    this.recentCommands = new Map(); // relayId -> {command, timestamp}
    this.deduplicationWindow = 2000; // 2 seconds
    
    // Health monitoring
    this.stats = {
      totalProcessed: 0,
      totalFailed: 0,
      totalDeduped: 0,
      rateLimitHits: 0
    };
    
    // Cleanup intervals
    this.rateLimitInterval = setInterval(() => this.cleanupRateLimit(), 1000);
    this.dedupeCleanupInterval = setInterval(() => this.cleanupDeduplication(), 5000);
    this.statsInterval = setInterval(() => this.logStats(), 30000);
  }

  enqueue(relayId, command, priority = 0) {
    // Jika rate-limit, tunda lalu coba enqueue ulang
    if (this.isRateLimited()) {
      const retryDelay = this.minInterval || 1000; // atau gunakan config Anda
      console.warn(`⚠️ Rate limit hit, retry enqueue relay ${relayId} in ${retryDelay}ms`);
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          this.enqueue(relayId, command, priority)
            .then(resolve)
            .catch(reject);
        }, retryDelay);
      });
    }

    // Check command deduplication
    const dedupe = this.checkDeduplication(relayId, command);
    if (dedupe.isDuplicate) {
      this.stats.totalDeduped++;
      console.log(`🔄 Deduped command for relay ${relayId}: ${command} (${dedupe.timeSince}ms ago)`);
      return Promise.resolve({ success: true, deduped: true });
    }

    if (!this.queues.has(relayId)) {
      this.queues.set(relayId, []);
    }

    let resolveFn, rejectFn;
    const promise = new Promise((resolve, reject) => {
      resolveFn = resolve;
      rejectFn = reject;
    });

    const cmdObj = {
      relayId,
      command,
      priority,
      timestamp: Date.now(),
      retries: 0,
      maxRetries: 3, // Increased from 2
      _resolve: resolveFn,
      _reject: rejectFn,
      id: `${relayId}_${command}_${Date.now()}`
    };

    // Enhanced queue depth management
    const q = this.queues.get(relayId);
    const queueLimit = this.getQueueLimitForPriority(priority);
    
    if (q.length >= queueLimit) {
      console.warn(`⚠️ Queue for relay ${relayId} is full (${q.length}/${queueLimit}), handling overflow`);
      this.handleQueueOverflow(q, cmdObj);
    }
    
    // Smart insertion based on priority and command type
    this.insertCommand(q, cmdObj);

    // Record command for deduplication
    this.recentCommands.set(relayId, {
      command,
      timestamp: Date.now()
    });

    // Intelligent processing scheduling
    this.scheduleProcessing();
    
    return promise;
  }

  getQueueLimitForPriority(priority) {
    if (priority >= 3) return 5;  // High priority (blink)
    if (priority >= 1) return 8;  // Medium priority (off)
    return 12; // Low priority (on)
  }

  handleQueueOverflow(queue, newCmd) {
    // Strategy: drop oldest low-priority commands first
    const lowPriorityIndex = queue.findIndex(cmd => cmd.priority === 0);
    if (lowPriorityIndex !== -1) {
      const dropped = queue.splice(lowPriorityIndex, 1)[0];
      if (dropped._reject) {
        dropped._reject(new Error('Queue overflow - low priority dropped'));
      }
      console.log(`🗑️ Dropped low priority command for queue space`);
    } else {
      // If no low priority, drop oldest
      const dropped = queue.shift();
      if (dropped._reject) {
        dropped._reject(new Error('Queue overflow'));
      }
    }
  }

  insertCommand(queue, cmdObj) {
    // Smart insertion: group by command type for batching efficiency
    const sameCommandIndex = queue.findIndex(
      cmd => cmd.command === cmdObj.command && cmd.priority === cmdObj.priority
    );
    
    if (sameCommandIndex !== -1) {
      // Insert after similar commands
      queue.splice(sameCommandIndex + 1, 0, cmdObj);
    } else {
      // Insert by priority
      const idx = queue.findIndex(item => item.priority < cmdObj.priority);
      if (idx === -1) queue.push(cmdObj);
      else queue.splice(idx, 0, cmdObj);
    }
  }

  checkDeduplication(relayId, command) {
    const recent = this.recentCommands.get(relayId);
    if (!recent) return { isDuplicate: false };
    
    const timeSince = Date.now() - recent.timestamp;
    const isDuplicate = recent.command === command && timeSince < this.deduplicationWindow;
    
    return { isDuplicate, timeSince };
  }

  scheduleProcessing() {
    const backoffDelay = this.getMinBackoffDelay();
    const baseDelay = this.isNearRateLimit() ? 200 : 50;
    const delay = Math.max(backoffDelay, baseDelay);
    
    setTimeout(() => this.processNext(), delay);
  }

  getMinBackoffDelay() {
    if (this.backoffDelays.size === 0) return 0;
    return Math.min(...this.backoffDelays.values());
  }

  async processNext() {
    if (this.currentProcessing >= this.maxConcurrent) return;
    
    // Enhanced rate limit check with burst protection
    // if (this.isRateLimited() || this.isBurstLimited()) {
    //   const waitTime = this.calculateRateLimitWaitTime();
    //   console.warn(`⚠️ Rate limit active, waiting ${waitTime}ms`);
    //   setTimeout(() => this.processNext(), waitTime);
    //   return;
    // }

    const { nextCmd, relayId } = this.selectNextCommand();
    if (!nextCmd) return;
    
    // Remove from queue
    this.queues.get(relayId).shift();
    
    this.processing.add(relayId);
    this.currentProcessing++;
    
    // Track for rate limiting
    const now = Date.now();
    this.commandsSentLastMinute.push(now);
    this.commandsSentLast10Seconds.push(now);

    try {
      await this.executeCommand(nextCmd);
      
      // Reset backoff on success
      this.backoffDelays.delete(relayId);
      this.stats.totalProcessed++;
      
    } catch (err) {
      this.stats.totalFailed++;
      console.error(`❌ [QUEUE] Error executing relay ${relayId}:`, err.message);
      
      await this.handleCommandFailure(nextCmd, err);
    } finally {
      this.processing.delete(relayId);
      this.currentProcessing--;
      
      // Cleanup empty queues
      if (this.queues.get(relayId)?.length === 0) {
        this.queues.delete(relayId);
      }
      
      // Continue processing
      this.scheduleProcessing();
    }
  }

  selectNextCommand() {
    let bestCmd = null;
    let bestRelayId = null;
    let highestScore = -1;
    
    for (const [relayId, queue] of this.queues) {
      if (this.processing.has(relayId) || queue.length === 0) continue;
      
      const cmd = queue[0];
      const score = this.calculateCommandScore(cmd, relayId);
      
      if (score > highestScore) {
        highestScore = score;
        bestCmd = cmd;
        bestRelayId = relayId;
      }
    }
    
    return { nextCmd: bestCmd, relayId: bestRelayId };
  }

  calculateCommandScore(cmd, relayId) {
    let score = cmd.priority * 10;
    
    // Prioritize commands with fewer retries
    score += (cmd.maxRetries - cmd.retries) * 2;
    
    // Deprioritize relays with recent failures
    const backoff = this.backoffDelays.get(relayId) || 0;
    score -= Math.log(backoff + 1);
    
    // Prioritize older commands
    const age = Date.now() - cmd.timestamp;
    score += Math.min(age / 1000, 10); // Max 10 points for age
    
    return score;
  }

  async handleCommandFailure(cmd, err) {
    if (cmd.retries < cmd.maxRetries) {
      cmd.retries++;
      
      // Enhanced backoff calculation
      const currentBackoff = this.backoffDelays.get(cmd.relayId) || this.baseBackoffDelay;
      const nextBackoff = Math.min(
        currentBackoff * this.backoffMultiplier,
        this.maxBackoffDelay
      );
      this.backoffDelays.set(cmd.relayId, nextBackoff);
      
      // Add jitter to prevent thundering herd
      const jitter = Math.random() * 0.3 * nextBackoff;
      const finalBackoff = nextBackoff + jitter;
      
      console.log(`🔄 Retry ${cmd.retries}/${cmd.maxRetries} for relay ${cmd.relayId} after ${Math.round(finalBackoff)}ms`);
      
      // Re-queue with same priority
      setTimeout(() => {
        if (this.queues.has(cmd.relayId)) {
          this.queues.get(cmd.relayId).unshift(cmd);
          this.scheduleProcessing();
        }
      }, finalBackoff);
    } else {
      // Max retries reached
      if (cmd._reject) {
        cmd._reject(err);
      }
      console.error(`❌ Max retries reached for relay ${cmd.relayId} command ${cmd.command}`);
    }
  }

  async executeCommand(cmdObj) {
    const start = Date.now();
    console.log(`🔄 [QUEUE] relay ${cmdObj.relayId}: ${cmdObj.command.toUpperCase()} (attempt ${cmdObj.retries + 1})`);
    
    try {
      const result = await sendRelayCommandDirect(cmdObj.relayId, cmdObj.command);
      
      const duration = Date.now() - start;
      console.log(`✅ [QUEUE] relay ${cmdObj.relayId} ${cmdObj.command.toUpperCase()} done in ${duration}ms`);
      
      if (cmdObj._resolve) {
        cmdObj._resolve(result);
      }
      
      this.emit(`completed_${cmdObj.relayId}_${cmdObj.timestamp}`, result);
      
    } catch (err) {
      // Enhanced error classification
      err.commandId = cmdObj.id;
      err.relayId = cmdObj.relayId;
      err.attempt = cmdObj.retries + 1;
      
      console.error(`❌ [QUEUE] Command failed for relay ${cmdObj.relayId}:`, {
        command: cmdObj.command,
        error: err.message,
        code: err.code,
        retries: cmdObj.retries,
        duration: Date.now() - start
      });
      
      throw err;
    }
  }

  isRateLimited() {
    this.cleanupRateLimit();
    return this.commandsSentLastMinute.length >= this.maxCommandsPerMinute;
  }

  isBurstLimited() {
    const tenSecondsAgo = Date.now() - 10000;
    this.commandsSentLast10Seconds = this.commandsSentLast10Seconds.filter(
      timestamp => timestamp > tenSecondsAgo
    );
    return this.commandsSentLast10Seconds.length >= this.maxCommandsPer10Seconds;
  }

  isNearRateLimit() {
    this.cleanupRateLimit();
    const minuteThreshold = this.maxCommandsPerMinute * 0.7;
    const burstThreshold = this.maxCommandsPer10Seconds * 0.8;
    
    return this.commandsSentLastMinute.length >= minuteThreshold ||
           this.commandsSentLast10Seconds.length >= burstThreshold;
  }

  calculateRateLimitWaitTime() {
    const burstOverage = this.commandsSentLast10Seconds.length - this.maxCommandsPer10Seconds;
    const minuteOverage = this.commandsSentLastMinute.length - this.maxCommandsPerMinute;
    
    if (burstOverage > 0) {
      return Math.min(2000 + (burstOverage * 500), 5000);
    }
    
    if (minuteOverage > 0) {
      return Math.min(1000 + (minuteOverage * 200), 3000);
    }
    
    return 1000;
  }

  cleanupRateLimit() {
    const oneMinuteAgo = Date.now() - 60000;
    const tenSecondsAgo = Date.now() - 10000;
    
    this.commandsSentLastMinute = this.commandsSentLastMinute.filter(
      timestamp => timestamp > oneMinuteAgo
    );
    
    this.commandsSentLast10Seconds = this.commandsSentLast10Seconds.filter(
      timestamp => timestamp > tenSecondsAgo
    );
  }

  cleanupDeduplication() {
    const cutoff = Date.now() - this.deduplicationWindow;
    for (const [relayId, data] of this.recentCommands.entries()) {
      if (data.timestamp < cutoff) {
        this.recentCommands.delete(relayId);
      }
    }
  }

  logStats() {
    if (this.stats.totalProcessed > 0 || this.stats.totalFailed > 0) {
      console.log(`📊 Queue Stats: Processed=${this.stats.totalProcessed}, Failed=${this.stats.totalFailed}, Deduped=${this.stats.totalDeduped}, RateLimits=${this.stats.rateLimitHits}`);
      
      // Reset stats
      this.stats = {
        totalProcessed: 0,
        totalFailed: 0,
        totalDeduped: 0,
        rateLimitHits: 0
      };
    }
  }

  getStatus() {
    this.cleanupRateLimit();
    
    const status = {
      totalQueued: 0,
      processing: this.processing.size,
      currentProcessing: this.currentProcessing,
      maxConcurrent: this.maxConcurrent,
      
      // Rate limiting info
      commandsLastMinute: this.commandsSentLastMinute.length,
      maxCommandsPerMinute: this.maxCommandsPerMinute,
      commandsLast10Seconds: this.commandsSentLast10Seconds.length,
      maxCommandsPer10Seconds: this.maxCommandsPer10Seconds,
      
      // Status flags
      rateLimited: this.isRateLimited(),
      burstLimited: this.isBurstLimited(),
      nearLimit: this.isNearRateLimit(),
      
      // Queue details
      queues: {},
      backoffs: {},
      
      // Stats
      stats: { ...this.stats }
    };
    
    for (const [rid, q] of this.queues) {
      status.totalQueued += q.length;
      status.queues[rid] = {
        count: q.length,
        priorities: q.reduce((acc, cmd) => {
          acc[cmd.priority] = (acc[cmd.priority] || 0) + 1;
          return acc;
        }, {})
      };
    }
    
    for (const [rid, delay] of this.backoffDelays) {
      status.backoffs[rid] = Math.round(delay);
    }
    
    return status;
  }

  clearQueue(relayId) {
    const queue = this.queues.get(relayId);
    if (queue) {
      // Reject all pending commands
      for (const cmd of queue) {
        if (cmd._reject) {
          cmd._reject(new Error('Queue cleared'));
        }
      }
      this.queues.delete(relayId);
      console.log(`🗑 [QUEUE] Cleared queue for relay ${relayId} (${queue.length} commands)`);
    }
    
    // Clear backoff and recent commands
    this.backoffDelays.delete(relayId);
    this.recentCommands.delete(relayId);
  }

  // Emergency methods
  pauseProcessing(duration = 5000) {
    console.warn(`⏸️ Pausing queue processing for ${duration}ms`);
    this.paused = true;
    setTimeout(() => {
      this.paused = false;
      console.log(`▶️ Resuming queue processing`);
      this.scheduleProcessing();
    }, duration);
  }

  getHealthStatus() {
    const status = this.getStatus();
    const health = {
      healthy: true,
      issues: [],
      recommendations: []
    };

    // Check for issues
    if (status.rateLimited || status.burstLimited) {
      health.healthy = false;
      health.issues.push('Rate limited');
      health.recommendations.push('Reduce command frequency');
    }

    if (status.totalQueued > 50) {
      health.healthy = false;
      health.issues.push('High queue depth');
      health.recommendations.push('Check relay connectivity');
    }

    if (Object.keys(status.backoffs).length > 10) {
      health.healthy = false;
      health.issues.push('Many failing relays');
      health.recommendations.push('Check network/hardware');
    }

    return health;
  }

  destroy() {
    console.log('🛑 Shutting down relay queue...');
    
    clearInterval(this.rateLimitInterval);
    clearInterval(this.dedupeCleanupInterval);
    clearInterval(this.statsInterval);
    
    // Reject all pending commands
    for (const [relayId, queue] of this.queues) {
      for (const cmd of queue) {
        if (cmd._reject) {
          cmd._reject(new Error('Queue destroyed'));
        }
      }
    }
    
    this.queues.clear();
    this.processing.clear();
    this.backoffDelays.clear();
    this.recentCommands.clear();
    this.commandsSentLastMinute = [];
    this.commandsSentLast10Seconds = [];
  }
}

// Single shared instance
const relayQueue = new RelayCommandQueue();

// Graceful shutdown handlers
process.on('SIGINT', () => {
  relayQueue.destroy();
  process.exit(0);
});

process.on('SIGTERM', () => {
  relayQueue.destroy();
  process.exit(0);
});

// Health monitoring endpoint data
relayQueue.on('healthCheck', () => {
  const health = relayQueue.getHealthStatus();
  if (!health.healthy) {
    console.warn('🔴 Queue health issues:', health.issues.join(', '));
  }
});

module.exports = { relayQueue };