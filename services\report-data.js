const db = require('../config/db');

/**
 * Men<PERSON><PERSON>lkan data laporan terstruktur antara startDate dan endDate (inklusif).
 * Setiap "business date" dihitung dari D 08:00 → (D+1) 03:00.
 */
async function generateReportData(startDate, endDate) {
  const start = new Date(startDate);
  const end   = new Date(endDate);
  const daily = [];
  let summary = {
    totalSessions: 0,
    dejavu_cash: 0,
    dejavu_tf: 0,
    food_cash: 0,
    food_tf: 0,
    service_cash: 0,
    service_tf: 0,
    gloves_cash: 0,
    gloves_tf: 0,
    totalRevenue: 0,
    activeBilliardOrders: 0
  };

  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    const iso     = d.toISOString().slice(0, 10);
    const next    = new Date(d);
    next.setDate(next.getDate() + 1);
    const isoNext = next.toISOString().slice(0, 10);
    const windowStart = `${iso} 08:00:00`;
    const windowEnd   = `${isoNext} 03:00:00`;

    // Ambil sessions dalam window bisnis
    const [sessions] = await db.query(`
      SELECT s.id, s.payment_method, s.total_billiard, s.tax_amount, s.status
      FROM sessions s
      JOIN billiard_orders bo ON bo.session_id = s.id
      WHERE (
        CASE WHEN bo.start_time < '03:00:00'
          THEN CONCAT(DATE_ADD(s.date, INTERVAL 1 DAY), ' ', bo.start_time)
          ELSE CONCAT(s.date, ' ', bo.start_time)
        END
      ) BETWEEN ? AND ?
      GROUP BY s.id
    `, [windowStart, windowEnd]);

    const sessionIds = sessions.map(r => r.id);
    const idList = sessionIds.length ? sessionIds.join(',') : 'NULL';

    // Hitung agregasi billiard dan service charge
    let dejavu_cash = 0, dejavu_tf = 0;
    let service_cash = 0, service_tf = 0;
    sessions.forEach(s => {
      if (s.payment_method === 'cash') {
        dejavu_cash += s.total_billiard || 0;
        service_cash += s.tax_amount   || 0;
      } else {
        dejavu_tf   += s.total_billiard || 0;
        service_tf   += s.tax_amount   || 0;
      }
    });

    // Ambil data makanan & sarung tangan
    const [foodRows] = await db.query(`
      SELECT s.payment_method, f.name, SUM(i.total_amount) AS total
      FROM food_order_items i
      JOIN foods f ON f.id = i.food_id
      JOIN sessions s ON s.id = i.session_id
      WHERE s.id IN (${idList})
      GROUP BY s.payment_method, f.name
    `);

    let food_cash = 0, food_tf = 0;
    let gloves_cash = 0, gloves_tf = 0;
    foodRows.forEach(row => {
      const isGlove = row.name === 'Sarung Tangan';
      if (row.payment_method === 'cash') {
        if (isGlove) gloves_cash += row.total;
        else         food_cash   += row.total;
      } else {
        if (isGlove) gloves_tf   += row.total;
        else         food_tf     += row.total;
      }
    });

    // Hitung total harian dan jumlah sesi
    const dailyTotal =
      dejavu_cash + dejavu_tf +
      food_cash   + food_tf   +
      service_cash + service_tf +
      gloves_cash + gloves_tf;
    const dailySessions = sessions.length;

    // Simpan ke array daily dengan detail per kategori
    daily.push({
      date: iso,
      dejavu_cash,
      dejavu_tf,
      food_cash,
      food_tf,
      service_cash,
      service_tf,
      gloves_cash,
      gloves_tf,
      dailyTotal,
      dailySessions
    });

    // Update ringkasan summary
    summary.totalSessions      += dailySessions;
    summary.dejavu_cash        += dejavu_cash;
    summary.dejavu_tf          += dejavu_tf;
    summary.food_cash          += food_cash;
    summary.food_tf            += food_tf;
    summary.service_cash       += service_cash;
    summary.service_tf         += service_tf;
    summary.gloves_cash        += gloves_cash;
    summary.gloves_tf          += gloves_tf;
    summary.totalRevenue       += dailyTotal;
    summary.activeBilliardOrders += sessions.filter(s => s.status === 1).length;
  }

  return { summary, daily, billiardDetails: [], foodDetails: [] };
}

module.exports = { generateReportData };
