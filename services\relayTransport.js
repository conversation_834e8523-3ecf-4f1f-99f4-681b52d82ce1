// // services/relayTransport.js
// const db  = require('../config/db');
// const net = require('net');

// const CONNECTION_TIMEOUT = 2500;
// const PORT              = 80;

// async function sendRelayCommandDirect(relayId, action) {
//   const [rows] = await db.query(
//     'SELECT ip_address, relay_name FROM relay WHERE id = ?', [relayId]
//   );
//   if (rows.length === 0) throw new Error('Relay tidak ditemukan');
//   const { ip_address, relay_name } = rows[0];
//   if (!ip_address) throw new Error(`Relay ${relay_name} tidak memiliki IP address`);

//   const command = action === 'on' ? `n${relayId}` : `f${relayId}`;
//   return new Promise((resolve, reject) => {
//     const socket = new net.Socket();
//     let done = false;

//     socket.setKeepAlive(true);
//     socket.setNoDelay(true);
//     socket.setTimeout(CONNECTION_TIMEOUT);

//     socket.once('error', err => {
//       if (!done) {
//         done = true;
//         socket.destroy();
//         return reject(new Error(`Connection error: ${err.message}`));
//       }
//     });

//     socket.once('timeout', () => {
//       if (!done) {
//         done = true;
//         socket.destroy();
//         return reject(new Error('Connection timeout'));
//       }
//     });

//     socket.connect(PORT, ip_address, () => {
//       socket.write(command + '\n', err => {
//         if (err) {
//           done = true;
//           socket.destroy();
//           return reject(new Error(`Write error: ${err.message}`));
//         }
//         // sukses
//         done = true;
//         socket.end();
//         resolve({ success: true, command, relay_name, to: ip_address });
//       });
//     });
//   });
// }

// module.exports = { sendRelayCommandDirect };

// services/relayTransport.js
const db = require('../config/db');
const { RelayConnectionPool } = require('./relayConnectionPool');

// Singleton connection pool
const connectionPool = new RelayConnectionPool({
  maxConnectionsPerIP: 2,      // Max 2 koneksi per IP relay
  connectionTimeout: 3000,      // 3 detik timeout
  idleTimeout: 30000,          // 30 detik idle sebelum ditutup
  retryDelay: 1000             // 1 detik delay antar retry
});

// Monitor pool status setiap 30 detik
setInterval(() => {
  const status = connectionPool.getStatus();
  console.log('📊 Connection Pool Status:', JSON.stringify(status));
}, 30000);

async function sendRelayCommandDirect(relayId, action) {
  const [rows] = await db.query(
    'SELECT ip_address, relay_name FROM relay WHERE id = ?', 
    [relayId]
  );
  
  if (rows.length === 0) {
    throw new Error('Relay tidak ditemukan');
  }
  
  const { ip_address, relay_name } = rows[0];
  if (!ip_address) {
    throw new Error(`Relay ${relay_name} tidak memiliki IP address`);
  }

  const command = action === 'on' ? `n${relayId}` : `f${relayId}`;
  
  try {
    const result = await connectionPool.sendCommand(ip_address, command);
    return { 
      ...result, 
      relay_name,
      relayId 
    };
  } catch (err) {
    console.error(`❌ Failed to send command to relay ${relayId} (${relay_name}):`, err.message);
    throw err;
  }
}

// Cleanup on process exit
process.on('SIGINT', () => {
  console.log('🔌 Closing connection pool...');
  connectionPool.destroy();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('🔌 Closing connection pool...');
  connectionPool.destroy();
  process.exit(0);
});

module.exports = { sendRelayCommandDirect };