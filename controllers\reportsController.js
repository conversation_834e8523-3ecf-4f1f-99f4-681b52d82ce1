// controllers/reportcontroller.js
const { generateReportData } = require('../services/report-data');

exports.getReport = async (req, res) => {
  const { start, end } = req.query;

  // Validasi wajibnya param start & end
  if (!start || !end) {
    return res
      .status(400)
      .json({ error: 'Query params "start" dan "end" wajib diisi (format YYYY-MM-DD).' });
  }

  try {
    // Panggil service yang sudah implementasi logika 08:00→03:00
    const report = await generateReportData(start, end);

    // Kirim data langsung—sesuai ReportData.fromJson
    return res.json(report);

  } catch (err) {
    console.error('[REPORTS] ERROR di getReport:', err);
    return res
      .status(500)
      .json({ error: 'Internal server error saat mengambil data laporan.' });
  }
};
