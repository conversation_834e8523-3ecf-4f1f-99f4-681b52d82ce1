// routes/relayMonitor.js - API untuk monitoring relay performance dengan 32 relay
const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { relayQueue } = require('../services/relayQueue');
const { getBlinkingRelays } = require('../jobs/relayCron');

/* *
 * GET /api/relay-monitor/status
 * Comprehensive relay system status untuk 32 relay */

router.get('/status', async (req, res) => {
  try {
    // 1) Get all relay basic info
    const [relays] = await db.query(`
      SELECT id, relay_name, status, ip_address, 
             created_at, updated_at
      FROM relay 
      ORDER BY id ASC
    `);

    // 2) Get queue status
    const queueStatus = relayQueue.getStatus();

    // 3) Get blinking relays
    const blinkingRelays = getBlinkingRelays();
    const blinkingStatus = Array.from(blinkingRelays.entries()).map(([id, data]) => ({
      relayId: id,
      name: data.name,
      startTime: data.startTime,
      duration: Date.now() - data.startTime,
      times: data.times || 'unknown'
    }));

    // 4) Get recent relay logs (last 50 entries)
    const [recentLogs] = await db.query(`
      SELECT relay_number, action, status, created_at, error_message
      FROM relay_logs 
      ORDER BY created_at DESC 
      LIMIT 50
    `);

    // 5) Calculate relay performance metrics
    const [performanceStats] = await db.query(`
      SELECT 
        relay_number,
        COUNT(*) as total_commands,
        SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_commands,
        SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_commands,
        ROUND(
          (SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 
          2
        ) as success_rate
      FROM relay_logs 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
      GROUP BY relay_number
      ORDER BY relay_number
    `);

    // 6) Network connectivity status
    const { relayConnections } = require('../services/relayLan');
    const networkStatus = Array.from(relayConnections.entries()).map(([ip, status]) => ({
      ip_address: ip,
      connection_status: status
    }));

    res.json({
      timestamp: new Date().toISOString(),
      summary: {
        total_relays: relays.length,
        active_relays: relays.filter(r => r.status === 1).length,
        queued_commands: queueStatus.totalQueued,
        processing_commands: queueStatus.processing,
        blinking_relays: blinkingStatus.length,
        network_connections: networkStatus.length
      },
      relays: relays.map(relay => {
        const perf = performanceStats.find(p => p.relay_number === relay.id);
        const network = networkStatus.find(n => n.ip_address === relay.ip_address);
        const isBlinking = blinkingStatus.find(b => b.relayId === relay.id);
        
        return {
          ...relay,
          performance: perf || { total_commands: 0, successful_commands: 0, failed_commands: 0, success_rate: 0 },
          network_status: network?.connection_status || 'unknown',
          is_blinking: !!isBlinking,
          blink_duration: isBlinking?.duration || 0
        };
      }),
      queue_status: queueStatus,
      blinking_relays: blinkingStatus,
      recent_logs: recentLogs.slice(0, 20), // Last 20 logs
      network_status: networkStatus
    });

  } catch (error) {
    console.error('❌ [MONITOR] Error getting relay status:', error);
    res.status(500).json({
      error: 'Failed to get relay status',
      message: error.message
    });
  }
});

/* *
 * GET /api/relay-monitor/performance/:relayId
 * Detailed performance analysis untuk relay tertentu */

router.get('/performance/:relayId', async (req, res) => {
  try {
    const { relayId } = req.params;
    const hours = parseInt(req.query.hours) || 24;

    // Get detailed logs for specific relay
    const [logs] = await db.query(`
      SELECT 
        action, status, created_at, error_message,
        table_number, customer_name
      FROM relay_logs 
      WHERE relay_number = ? 
        AND created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
      ORDER BY created_at DESC
      LIMIT 200
    `, [relayId, hours]);

    // Calculate hourly performance
    const [hourlyStats] = await db.query(`
      SELECT 
        HOUR(created_at) as hour,
        COUNT(*) as total_commands,
        SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successful,
        SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed
      FROM relay_logs 
      WHERE relay_number = ? 
        AND created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
      GROUP BY HOUR(created_at)
      ORDER BY hour
    `, [relayId, hours]);

    // Get relay info
    const [relayInfo] = await db.query(`
      SELECT id, relay_name, status, ip_address 
      FROM relay 
      WHERE id = ?
    `, [relayId]);

    if (relayInfo.length === 0) {
      return res.status(404).json({ error: 'Relay not found' });
    }

    res.json({
      relay: relayInfo[0],
      time_range: `${hours} hours`,
      summary: {
        total_logs: logs.length,
        successful_commands: logs.filter(l => l.status === 'SUCCESS').length,
        failed_commands: logs.filter(l => l.status === 'FAILED').length,
        success_rate: logs.length > 0 ? 
          Math.round((logs.filter(l => l.status === 'SUCCESS').length / logs.length) * 100) : 0
      },
      hourly_performance: hourlyStats,
      recent_logs: logs.slice(0, 50),
      error_analysis: logs
        .filter(l => l.status === 'FAILED' && l.error_message)
        .reduce((acc, log) => {
          acc[log.error_message] = (acc[log.error_message] || 0) + 1;
          return acc;
        }, {})
    });

  } catch (error) {
    console.error('❌ [MONITOR] Error getting relay performance:', error);
    res.status(500).json({
      error: 'Failed to get relay performance',
      message: error.message
    });
  }
});

/* *
 * POST /api/relay-monitor/force-clear-queue
 * Emergency: Clear queue untuk relay tertentu */

router.post('/force-clear-queue', async (req, res) => {
  try {
    const { relayId } = req.body;
    
    if (!relayId) {
      return res.status(400).json({ error: 'relayId is required' });
    }

    relayQueue.clearQueue(relayId);
    
    res.json({
      success: true,
      message: `Queue cleared for relay ${relayId}`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [MONITOR] Error clearing queue:', error);
    res.status(500).json({
      error: 'Failed to clear queue',
      message: error.message
    });
  }
});

/* *
 * GET /api/relay-monitor/network-test
 * Test konektivitas ke semua relay */

router.get('/network-test', async (req, res) => {
  try {
    const [relays] = await db.query(`
      SELECT id, relay_name, ip_address 
      FROM relay 
      WHERE ip_address IS NOT NULL AND ip_address != ''
      ORDER BY id
    `);

    const net = require('net');
    const testResults = [];

    // Test each relay connection
    for (const relay of relays) {
      const testResult = await new Promise((resolve) => {
        const socket = new net.Socket();
        const startTime = Date.now();
        
        socket.setTimeout(3000);
        
        socket.connect(8081, relay.ip_address, () => {
          const responseTime = Date.now() - startTime;
          socket.destroy();
          resolve({
            relayId: relay.id,
            relayName: relay.relay_name,
            ipAddress: relay.ip_address,
            status: 'connected',
            responseTime: responseTime
          });
        });
        
        socket.on('error', (err) => {
          resolve({
            relayId: relay.id,
            relayName: relay.relay_name,
            ipAddress: relay.ip_address,
            status: 'failed',
            error: err.message,
            responseTime: null
          });
        });
        
        socket.on('timeout', () => {
          socket.destroy();
          resolve({
            relayId: relay.id,
            relayName: relay.relay_name,
            ipAddress: relay.ip_address,
            status: 'timeout',
            responseTime: null
          });
        });
      });
      
      testResults.push(testResult);
    }

    const summary = {
      total_tested: testResults.length,
      connected: testResults.filter(r => r.status === 'connected').length,
      failed: testResults.filter(r => r.status === 'failed').length,
      timeout: testResults.filter(r => r.status === 'timeout').length,
      average_response_time: testResults
        .filter(r => r.responseTime !== null)
        .reduce((sum, r) => sum + r.responseTime, 0) / 
        testResults.filter(r => r.responseTime !== null).length || 0
    };

    res.json({
      timestamp: new Date().toISOString(),
      summary,
      results: testResults
    });

  } catch (error) {
    console.error('❌ [MONITOR] Error testing network:', error);
    res.status(500).json({
      error: 'Failed to test network',
      message: error.message
    });
  }
});

module.exports = router;
