const db = require('../config/db');
const dayjs = require('dayjs');

const getOwnerDashboard = async (req, res) => {
  try {
    const now = dayjs();

    // Total pemasukan dari semua waktu (status = 2 = selesai)
    const [incomeResult] = await db.query(`
      SELECT COALESCE(SUM(price - discount), 0) AS total_income
      FROM billiard_orders
      WHERE status = 2
    `);

    // Jumlah total reservasi
    const [reservationResult] = await db.query(`
      SELECT COUNT(*) AS total_reservations
      FROM billiard_orders
    `);

    // Total meja
    const [tableResult] = await db.query(`
      SELECT COUNT(*) AS total_tables
      FROM billiard_tables
    `);

    // Meja yang sedang aktif (status = 1 sekarang)
    const [activeTableResult] = await db.query(`
      SELECT COUNT(DISTINCT table_id) AS active_tables
      FROM billiard_orders
      WHERE status = 1
    `);

    // Ambil semua meja dan status terkini (jika ada order aktif)
    const [tables] = await db.query(`
      SELECT 
        t.table_number,
        COALESCE(o.status, 0) AS status,
        COALESCE(TIMESTAMPDIFF(SECOND, ?, o.end_time), 0) AS remaining_time_seconds,
        o.start_time,
        o.end_time
      FROM billiard_tables t
      LEFT JOIN billiard_orders o
        ON o.table_id = t.id AND o.status = 1
      ORDER BY t.table_number ASC
    `, [now.format('YYYY-MM-DD HH:mm:ss')]);

    res.status(200).json({
      success: true,
      data: {
        total_income: incomeResult[0].total_income,
        total_reservations: reservationResult[0].total_reservations,
        total_tables: tableResult[0].total_tables,
        active_tables: activeTableResult[0].active_tables,
        tables: tables.map((t) => ({
          table_number: t.table_number,
          status: t.status,
          remaining_time_seconds: t.remaining_time_seconds > 0 ? t.remaining_time_seconds : 0,
          start_time: t.start_time,
          end_time: t.end_time,
        })),
      }
    });
  } catch (error) {
    console.error('Dashboard Error:', error);
    res.status(500).json({ success: false, message: 'Internal Server Error' });
  }
};

module.exports = {
  getOwnerDashboard,
};
