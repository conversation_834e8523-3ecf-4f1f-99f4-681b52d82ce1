const crypto = require('crypto'); // Using SHA256 for hashing
const jwt = require('jsonwebtoken');
const db = require('../config/db'); // Ensure correct DB import
require('dotenv').config();

/**
 * @desc Hash password using SHA256
 */
function hashPassword(password) {
  return crypto.createHash('sha256').update(password).digest('hex');
}

/**
 * @desc User Registration
 * @route POST /api/auth/register
 */
exports.register = async (req, res) => {
  const { username, password, full_name } = req.body;
  console.log(`[REGISTER] Attempting to register: ${username}`);

  try {
    const [existingUsers] = await db.query(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );

    if (existingUsers.length > 0) {
      console.warn(`[REGISTER] Username already exists: ${username}`);
      return res.status(400).json({ error: 'Username already exists' });
    }

    const hashedPassword = hashPassword(password);
    await db.query(
      'INSERT INTO users (username, password, full_name) VALUES (?, ?, ?)',
      [username, hashedPassword, full_name]
    );

    console.log(`[REGISTER] User registered: ${username}`);
    res.status(201).json({ message: 'User registered successfully' });
  } catch (error) {
    console.error('[REGISTER] Database error:', error);
    res.status(500).json({ error: 'Database error' });
  }
};

/**
 * @desc User Login
 * @route POST /api/auth/login
 */
exports.login = async (req, res) => {
  const { username, password } = req.body;
  console.log(`[LOGIN] Attempting login: ${username}`);

  try {
    const [users] = await db.query(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );

    if (users.length === 0) {
      console.warn(`[LOGIN] User not found: ${username}`);
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const user = users[0];
    const hashedPassword = hashPassword(password);

    if (user.password !== hashedPassword) {
      console.warn(`[LOGIN] Incorrect password for: ${username}`);
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const token = jwt.sign(
      { id: user.id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: '2h' }
    );

    console.log(`[LOGIN] Login successful: ${username}`);
    res.status(200).json({ message: 'Login successful', token });
  } catch (error) {
    console.error('[LOGIN] Database error:', error);
    res.status(500).json({ error: 'Database error' });
  }
};

/**
 * @desc Get User Profile
 * @route GET /api/auth/profile
 * @access Private (Requires Token)
 */
exports.getUserProfile = async (req, res) => {
  console.log(`[PROFILE] Fetching user profile for ID: ${req.user.id}`);

  try {
    const [users] = await db.query(
      'SELECT id, username, full_name FROM users WHERE id = ?',
      [req.user.id]
    );

    if (users.length === 0) {
      console.warn(`[PROFILE] User not found: ID ${req.user.id}`);
      return res.status(404).json({ error: 'User not found' });
    }

    res.status(200).json({ user: users[0] });
  } catch (error) {
    console.error('[PROFILE] Database error:', error);
    res.status(500).json({ error: 'Database error' });
  }
};

/**
 * @desc User Logout
 * @route POST /api/auth/logout
 */
exports.logout = (req, res) => {
  console.log('[LOGOUT] User logged out successfully');
  res.status(200).json({ message: 'User logged out successfully' });
};

/**
 * @desc Middleware to Verify Token
 */
exports.verifyToken = (req, res, next) => {
  const token = req.header('Authorization')?.split(' ')[1];

  if (!token) {
    console.warn('[AUTH] Access denied - No token provided');
    return res.status(403).json({ error: 'Access denied' });
  }

  try {
    const verified = jwt.verify(token, process.env.JWT_SECRET);
    req.user = verified;
    console.log(`[AUTH] Token verified - User ID: ${verified.id}`);
    next();
  } catch (error) {
    console.error('[AUTH] Invalid token:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
};

/**
 * @desc Check Token Validity (Buat Flutter App Pas Startup)
 * @route GET /api/auth/check-token
 * @access Private (Requires Token)
 */
exports.checkToken = (req, res) => {
  const token = req.header('Authorization')?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }

  try {
    const verified = jwt.verify(token, process.env.JWT_SECRET);
    res.status(200).json({ message: 'Token is valid', user: verified });
  } catch (error) {
    return res.status(401).json({ error: 'Invalid or expired token' });
  }
};
