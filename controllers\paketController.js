const db      = require("../config/db");

// GET /packages
exports.getAllPackages = async (req, res) => {
  try {
    const [results] = await db.query(`
      SELECT 
        p.id, 
        p.nama_paket, 
        p.deskripsi,
        pp.duration, 
        pp.price + 0 AS price 
      FROM packages p
      LEFT JOIN package_prices pp 
        ON p.id = pp.package_id
      ORDER BY p.created_at DESC, pp.duration
    `);

    // Group by package id
    const packagesMap = {};
    for (const row of results) {
      const id = row.id;
      if (!packagesMap[id]) {
        packagesMap[id] = {
          id,
          nama_paket: row.nama_paket,
          deskripsi: row.deskripsi,
          harga: []
        };
      }
      if (row.duration != null) {
        packagesMap[id].harga.push({
          duration: row.duration,
          price: row.price
        });
      }
    }

    res.json(Object.values(packagesMap));
  } catch (err) {
    console.error("Error fetching packages:", err);
    res.status(500).json({ error: err.message });
  }
};

// GET /packages/:id
exports.getPackageById = async (req, res) => {
  const { id } = req.params;
  try {
    const [rows] = await db.query(`
      SELECT 
        p.id, 
        p.nama_paket, 
        p.deskripsi,
        pp.duration, 
        pp.price
      FROM packages p
      LEFT JOIN package_prices pp 
        ON p.id = pp.package_id
      WHERE p.id = ?
      ORDER BY pp.duration
    `, [id]);

    if (rows.length === 0) {
      return res.status(404).json({ message: "Paket tidak ditemukan" });
    }

    const pkg = {
      id: rows[0].id,
      nama_paket: rows[0].nama_paket,
      deskripsi: rows[0].deskripsi,
      harga: []
    };
    for (const r of rows) {
      if (r.duration != null) {
        pkg.harga.push({ duration: r.duration, price: r.price });
      }
    }

    res.json(pkg);
  } catch (err) {
    console.error("Error fetching package by ID:", err);
    res.status(500).json({ error: err.message });
  }
};

// // POST /packages
// exports.createPackage = async (req, res) => {
//   const { nama_paket, deskripsi, harga } = req.body;
//   if (!nama_paket || !deskripsi || !Array.isArray(harga)) {
//     return res.status(400).json({ message: "nama_paket, deskripsi, dan harga[] diperlukan" });
//   }

//   const conn = await db.getConnection();
//   try {
//     await conn.beginTransaction();

//     // insert into packages
//     const [pkgRes] = await conn.query(
//       `INSERT INTO packages (nama_paket, deskripsi) VALUES (?, ?)`,
//       [nama_paket, deskripsi]
//     );
//     const pkgId = pkgRes.insertId;

//     // insert all price tiers
//     const priceRows = harga.map(h => [pkgId, h.duration, h.price]);
//     if (priceRows.length) {
//       await conn.query(
//         `INSERT INTO package_prices (package_id, duration, price) VALUES ?`,
//         [priceRows]
//       );
//     }

//     await conn.commit();
//     res.status(201).json({ message: "Paket berhasil ditambahkan", id: pkgId });
//   } catch (err) {
//     await conn.rollback();
//     console.error("Error creating package:", err);
//     res.status(500).json({ error: err.message });
//   } finally {
//     conn.release();
//   }
// };

// POST /packages
exports.createPackage = async (req, res) => {
  const { nama_paket, deskripsi, harga } = req.body;
  if (!nama_paket || !deskripsi || !Array.isArray(harga)) {
    return res.status(400).json({ message: "nama_paket, deskripsi, dan harga[] diperlukan" });
  }

  const conn = await db.getConnection();
  try {
    await conn.beginTransaction();

    // 1) insert into packages
    const [pkgRes] = await conn.query(
      `INSERT INTO packages (nama_paket, deskripsi) VALUES (?, ?)`,
      [nama_paket, deskripsi]
    );
    const pkgId = pkgRes.insertId;

    // 2) insert all price tiers, now including day_type
    //    note: package_prices must have been ALTERed to add `day_type`
    const priceRows = harga.map(h => [
      pkgId,
      h.duration,
      h.price,
      h.day_type || 'weekday'
    ]);
    if (priceRows.length) {
      await conn.query(
        `INSERT INTO package_prices (package_id, duration, price, day_type) VALUES ?`,
        [priceRows]
      );
    }

    await conn.commit();
    res.status(201).json({ message: "Paket berhasil ditambahkan", id: pkgId });
  } catch (err) {
    await conn.rollback();
    console.error("Error creating package:", err);
    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};


// // PUT /packages/:id
// exports.updatePackage = async (req, res) => {
//   const { id } = req.params;
//   const { nama_paket, deskripsi, harga } = req.body;
//   if (!nama_paket || !deskripsi || !Array.isArray(harga)) {
//     return res.status(400).json({ message: "nama_paket, deskripsi, dan harga[] diperlukan" });
//   }

//   const conn = await db.getConnection();
//   try {
//     await conn.beginTransaction();

//     // update package metadata
//     const [upd] = await conn.query(
//       `UPDATE packages SET nama_paket = ?, deskripsi = ? WHERE id = ?`,
//       [nama_paket, deskripsi, id]
//     );
//     if (upd.affectedRows === 0) {
//       await conn.rollback();
//       return res.status(404).json({ message: "Paket tidak ditemukan" });
//     }

//     // replace price tiers
//     await conn.query(`DELETE FROM package_prices WHERE package_id = ?`, [id]);
//     const priceRows = harga.map(h => [id, h.duration, h.price]);
//     if (priceRows.length) {
//       await conn.query(
//         `INSERT INTO package_prices (package_id, duration, price) VALUES ?`,
//         [priceRows]
//       );
//     }

//     await conn.commit();
//     res.json({ message: "Paket berhasil diperbarui" });
//   } catch (err) {
//     await conn.rollback();
//     console.error("Error updating package:", err);
//     res.status(500).json({ error: err.message });
//   } finally {
//     conn.release();
//   }
// };

// PUT /packages/:id
exports.updatePackage = async (req, res) => {
  const { id } = req.params;
  const { nama_paket, deskripsi, harga } = req.body;
  if (!nama_paket || !deskripsi || !Array.isArray(harga)) {
    return res.status(400).json({ message: "nama_paket, deskripsi, dan harga[] diperlukan" });
  }

  const conn = await db.getConnection();
  try {
    await conn.beginTransaction();

    // update package metadata
    const [upd] = await conn.query(
      `UPDATE packages SET nama_paket = ?, deskripsi = ? WHERE id = ?`,
      [nama_paket, deskripsi, id]
    );
    if (upd.affectedRows === 0) {
      await conn.rollback();
      return res.status(404).json({ message: "Paket tidak ditemukan" });
    }

    // replace price tiers (now with day_type)
    await conn.query(`DELETE FROM package_prices WHERE package_id = ?`, [id]);
    const priceRows = harga.map(h => [
      id,
      h.duration,
      h.price,
      h.day_type || 'weekday'
    ]);
    if (priceRows.length) {
      await conn.query(
        `INSERT INTO package_prices (package_id, duration, price, day_type) VALUES ?`,
        [priceRows]
      );
    }

    await conn.commit();
    res.json({ message: "Paket berhasil diperbarui" });
  } catch (err) {
    await conn.rollback();
    console.error("Error updating package:", err);
    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

// DELETE /packages/:id
exports.deletePackage = async (req, res) => {
  const { id } = req.params;
  const conn = await db.getConnection();
  try {
    await conn.beginTransaction();

    // delete dependent tiers first
    await conn.query(`DELETE FROM package_prices WHERE package_id = ?`, [id]);

    // then delete the package
    const [del] = await conn.query(`DELETE FROM packages WHERE id = ?`, [id]);
    if (del.affectedRows === 0) {
      await conn.rollback();
      return res.status(404).json({ message: "Paket tidak ditemukan" });
    }

    await conn.commit();
    res.json({ message: "Paket berhasil dihapus" });
  } catch (err) {
    await conn.rollback();
    console.error("Error deleting package:", err);
    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

// /**
//  * GET /packages/:packageId/prices?table_id=123
//  */
// exports.getPackagePrices = async (req, res) => {
//   const { packageId } = req.params;
//   const { table_id }  = req.query;
//   if (!table_id) {
//     return res.status(400).json({ message: "Missing required query param ?table_id" });
//   }

//   try {
//     // 1) fetch the table's type
//     const [[{ type: tableType }]] = await db.query(
//       "SELECT type FROM billiard_tables WHERE id = ?",
//       [table_id]
//     );

//     // 2) union override tiers and fallback
//     const [tiers] = await db.query(
//       `
//       SELECT duration, price
//         FROM type_package_prices
//        WHERE package_id = ? AND table_type = ?
//       UNION
//       SELECT duration, price
//         FROM package_prices
//        WHERE package_id = ?
//          AND duration NOT IN (
//            SELECT duration
//              FROM type_package_prices
//             WHERE package_id = ? AND table_type = ?
//          )
//       ORDER BY duration
//       `,
//       [packageId, tableType, packageId, packageId, tableType]
//     );

//     res.json(tiers);
//   } catch (err) {
//     console.error("[getPackagePrices] ❌", err);
//     res.status(500).json({ error: err.message });
//   }
// };

exports.getPackagePrices = async (req, res) => {
  const { packageId } = req.params;
  const { table_id }  = req.query;
  if (!table_id) {
    return res.status(400).json({ message: "Missing required query param ?table_id" });
  }

  try {
    // 1) cari tipe meja
    const [[{ type: tableType }]] = await db.query(
      "SELECT type FROM billiard_tables WHERE id = ?",
      [table_id]
    );

    // --- DI SINI kita deteksi DAY TYPE ---
    const today     = new Date();
    const dayNumber = today.getDay();                  // 0 = Minggu, 1 = Senin, …, 6 = Sabtu
    const isWeekend = (dayNumber === 0 || dayNumber === 6);
    const dayType   = isWeekend ? 'weekend' : 'weekday';
    // ----------------------------------------

    // 2) tarik tiers hanya untuk dayType yang sesuai
    const [tiers] = await db.query(
      `
      SELECT duration, price
        FROM type_package_prices
       WHERE package_id = ?
         AND table_type = ?
         AND day_type = ?
      UNION
      SELECT duration, price
        FROM package_prices
       WHERE package_id = ?
         AND day_type = ?
         AND duration NOT IN (
           SELECT duration
             FROM type_package_prices
            WHERE package_id = ?
              AND table_type = ?
              AND day_type = ?
         )
      ORDER BY duration
      `,
      [
        packageId, tableType, dayType,
        packageId, dayType,
        packageId, tableType, dayType
      ]
    );

    res.json(tiers);
  } catch (err) {
    console.error("[getPackagePrices] ❌", err);
    res.status(500).json({ error: err.message });
  }
};






