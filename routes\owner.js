const express = require('express');
const router = express.Router();
const { getOwnerDashboard } = require('../controllers/ownerDashboardController');
const { getRevenueDashboard } = require('../controllers/ownerRevenueController');

// Endpoint: GET /api/owner/owner (dashboard owner utama)
router.get('/owner', getOwnerDashboard);

// Endpoint: GET /api/owner/revenue (revenue per bulan)
router.get('/revenue', getRevenueDashboard);

module.exports = router;
