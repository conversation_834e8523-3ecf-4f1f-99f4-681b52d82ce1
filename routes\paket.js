const express = require("express");
const router  = express.Router();
const paketController = require("../controllers/paketController");

router.get("/",                     paketController.getAllPackages);
router.get("/:packageId/prices",    paketController.getPackagePrices);
router.get("/:id",                  paketController.getPackageById);
router.post("/",                    paketController.createPackage);
router.put("/:id",                  paketController.updatePackage);
router.delete("/:id",               paketController.deletePackage);

module.exports = router;
