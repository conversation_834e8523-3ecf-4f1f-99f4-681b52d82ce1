
// relayCron.js - Versi Fixed dengan Brute Force yang Reliable

const cron = require("node-cron");
const db   = require("../config/db");
const { relayQueue } = require("../services/relayQueue");
require("log-timestamp")(() => new Date().toISOString());

// ------------------------------------------------------
// State & config
// ------------------------------------------------------
const blinkingRelays = new Map();   
const failureCount   = new Map();   
const manualOnGrace  = new Map();   
const relaysNeedSync = new Set();
const lastSentState = new Map();   

const MAX_FAILURE    = 3;
const GRACE_PERIOD_MS = 5000;       
const MAX_PARALLEL_BLINK = 3;
let currentBlinking = 0;

// Brute force cycling untuk ensure semua relay tercek
let bruteForceIndex = 0;
const BRUTE_FORCE_BATCH_SIZE = 8;  // Process 8 relay per run
const BRUTE_FORCE_INTERVAL = 12;   // Setiap 12 detik

// ------------------------------------------------------
// Logging helper
// ------------------------------------------------------
async function insertRelayLog({
  orderId = null,
  tableNumber = null,
  relayNumber,
  action,      
  status,      
  customerName = null,
  errorMessage = null
}) {
  try {
    await db.query(`
      INSERT INTO relay_logs
        (order_id, table_number, relay_number, action, status, customer_name, error_message)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [orderId, tableNumber, relayNumber, action, status, customerName, errorMessage]);
  } catch (err) {
    console.error('? Failed to insert relay log:', err.message);
  }
}

// ------------------------------------------------------
// Reliable ON/OFF dengan deduplication
// ------------------------------------------------------
async function reliableOn(relayId) {
  const currentState = lastSentState.get(relayId);
  if (currentState === true) {
    return Promise.resolve();
  }
  
  return relayQueue.enqueue(relayId, "on", 0)
    .then(() => lastSentState.set(relayId, true))
    .catch(err => {
      console.error(`? reliableOn ${relayId} failed:`, err.message);
      throw err;
    });
}

async function reliableOff(relayId) {
  const currentState = lastSentState.get(relayId);
  if (currentState === false) {
    return Promise.resolve();
  }
  
  return relayQueue.enqueue(relayId, "off", 1)
    .then(() => lastSentState.set(relayId, false))
    .catch(err => {
      console.error(`? reliableOff ${relayId} failed:`, err.message);
      throw err;
    });
}

// ------------------------------------------------------
// Blink job dengan staggered execution
// ------------------------------------------------------
cron.schedule("*/55 * * * * *", async () => {
  console.log(`[BLINK JOB] ${new Date().toLocaleTimeString()}`);
  try {
    const [orders] = await db.query(`
      SELECT t.relay_id, r.relay_name
        FROM billiard_orders o
        JOIN sessions s         ON o.session_id = s.id
        JOIN billiard_tables t  ON o.table_id = t.id
        JOIN relay r            ON t.relay_id = r.id
        WHERE  TIMESTAMPDIFF(
                MINUTE,
                NOW(),
                CASE
                  WHEN o.start_time <= o.end_time AND o.start_time >= '08:00' THEN
                    CONCAT(DATE(s.date), ' ', o.end_time)
                  ELSE
                    CONCAT(DATE_ADD(DATE(s.date), INTERVAL 1 DAY), ' ', o.end_time)
                END
              ) = 15
    `);

    // Process dengan delay
    orders.forEach((o, i) => {
      setTimeout(() => {
        if (!blinkingRelays.has(o.relay_id)) {
          blinkRelay(o.relay_id, o.relay_name, 2);
        }
      }, i * 500);
    });

  } catch (e) {
    console.error("? BLINK JOB error:", e.message);
  }
}, { timezone: "Asia/Makassar" });

// ------------------------------------------------------
// CYCLING BRUTE FORCE - Guaranteed All Relay Coverage
// ------------------------------------------------------
let isReconciling = false;

cron.schedule(`*/${BRUTE_FORCE_INTERVAL} * * * * *`, async () => {
  if (isReconciling) {
    console.log(`? Reconcile already running, skip`);
    return;
  }

  isReconciling = true;
  const cycleStart = Date.now();
  console.log(`[BRUTE-FORCE] Cycle ${Math.floor(bruteForceIndex/BRUTE_FORCE_BATCH_SIZE) + 1} starting at relay ${bruteForceIndex + 1}`);

  try {
    // Get ALL relay states first
    // const [allRelays] = await db.query(`
    //   SELECT t.id AS table_id, t.relay_id, t.table_number, r.relay_name, r.status AS relay_status,
    //     EXISTS(
    //       SELECT 1 FROM billiard_orders o JOIN sessions s ON o.session_id=s.id
    //       WHERE o.table_id=t.id AND o.status=1 AND (
    //         (DATE(s.date)=CURDATE() AND o.start_time<=o.end_time AND TIME(NOW()) BETWEEN o.start_time AND o.end_time) OR
    //         (DATE(s.date)=CURDATE() AND o.start_time>o.end_time AND (TIME(NOW())>=o.start_time OR TIME(NOW())<=o.end_time)) OR
    //         (DATE(s.date)=DATE_SUB(CURDATE(),INTERVAL 1 DAY) AND o.start_time>o.end_time AND TIME(NOW())<=o.end_time)
    //       )
    //     ) AS active
    //   FROM billiard_tables t 
    //   JOIN relay r ON t.relay_id=r.id
    //   WHERE t.relay_id BETWEEN 1 AND 32
    //   ORDER BY t.relay_id
    // `);

    const [allRelays] = await db.query(`
      SELECT 
        t.id            AS table_id, 
        t.relay_id, 
        t.table_number, 
        r.relay_name, 
        r.status        AS relay_status,
        EXISTS(
          SELECT 1 
          FROM billiard_orders o 
          JOIN sessions s ON o.session_id = s.id
          WHERE o.table_id = t.id 
          -- ❶ Sesi belum lunas
            AND  COALESCE(s.pay_amount,0) <
                ( SELECT IFNULL(SUM(price - discount),0)
                  FROM   billiard_orders
                  WHERE  session_id = s.id )
            AND (
              -- Case 1: Same-day order
              (
                o.start_time <= o.end_time
                AND DATE(s.date) = CURDATE()
                AND TIME(NOW()) BETWEEN o.start_time AND o.end_time
              )
              OR
              -- Case 2: Cross-midnight � part 1
              (
                o.start_time > o.end_time
                AND DATE(s.date) = CURDATE()
                AND TIME(NOW()) >= GREATEST(o.start_time, '08:00:00')
              )
              OR
              -- Case 3: Cross-midnight � part 2
              (
                o.start_time > o.end_time
                AND DATE(s.date) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                AND TIME(NOW()) <= LEAST(o.end_time, '03:00:00')
              )
            )
        ) AS active
      FROM billiard_tables t 
      JOIN relay r ON t.relay_id = r.id
      WHERE t.relay_id BETWEEN 1 AND 32
      ORDER BY t.relay_id;
    `);

    // Process current batch
    const batchEnd = Math.min(bruteForceIndex + BRUTE_FORCE_BATCH_SIZE, allRelays.length);
    const currentBatch = allRelays.slice(bruteForceIndex, batchEnd);
    
    console.log(`  Processing relays ${bruteForceIndex + 1} to ${batchEnd} (${currentBatch.length} relays)`);

    // Process each relay in batch with staggered timing
    for (let i = 0; i < currentBatch.length; i++) {
      const { relay_id, relay_name, active, relay_status } = currentBatch[i];
      
      setTimeout(async () => {
        try {
          const targetState = Boolean(active);
          
          // Check queue depth first
          const queueStatus = relayQueue.getStatus();
          if (queueStatus.totalQueued > 20) {
            console.warn(`?? Queue overload (${queueStatus.totalQueued}), skipping relay ${relay_id}`);
            return;
          }

          // ALWAYS update DB regardless of current state
          await db.query("UPDATE relay SET status = ? WHERE id = ?", [targetState ? 1 : 0, relay_id]);

          // ALWAYS send command for reliability (deduplication handled in reliable functions)
          if (targetState) {
            await reliableOn(relay_id);
            manualOnGrace.set(relay_id, Date.now());
            console.log(`?? BRUTE: Relay ${relay_id} (${relay_name}) ? ON`);
          } else {
            // Check grace period for OFF
            const lastOn = manualOnGrace.get(relay_id);
            const now = Date.now();
            
            if (lastOn && (now - lastOn) < GRACE_PERIOD_MS) {
              console.log(`? Grace period active for relay ${relay_id}, defer OFF`);
              return;
            }
            
            await reliableOff(relay_id);
            console.log(`?? BRUTE: Relay ${relay_id} (${relay_name}) ? OFF`);
          }

        } catch (err) {
          console.error(`? BRUTE-FORCE failed for relay ${relay_id}:`, err.message);
        }
      }, i * 150); // 150ms delay between commands in batch
    }

    // Update index for next cycle
    bruteForceIndex = batchEnd;
    
    // Reset cycle when we reach the end
    if (bruteForceIndex >= allRelays.length) {
      bruteForceIndex = 0;
      console.log(`?? BRUTE-FORCE cycle completed, resetting to relay 1`);
    }

    const cycleDuration = Date.now() - cycleStart;
    console.log(`? BRUTE-FORCE batch processed in ${cycleDuration}ms, next start: relay ${bruteForceIndex + 1}`);

  } catch (err) {
    console.error("? BRUTE-FORCE error:", err.message);
  } finally {
    // Delay before allowing next reconcile
    setTimeout(() => {
      isReconciling = false;
    }, 2000);
  }
}, { timezone: "Asia/Makassar" });

// ------------------------------------------------------
// Status monitoring job
// ------------------------------------------------------
cron.schedule("*/30 * * * * *", async () => {
  const queueStatus = relayQueue.getStatus();
  const blinkCount = blinkingRelays.size;
  
  if (queueStatus.totalQueued > 15 || blinkCount > 2) {
    console.log(`?? STATUS: Queue=${queueStatus.totalQueued}, Processing=${queueStatus.processing}, Blinking=${blinkCount}, BruteIndex=${bruteForceIndex}`);
  }
  
  // Clear old grace periods
  const now = Date.now();
  for (const [relayId, timestamp] of manualOnGrace.entries()) {
    if (now - timestamp > GRACE_PERIOD_MS * 2) {
      manualOnGrace.delete(relayId);
    }
  }
}, { timezone: "Asia/Makassar" });

// ------------------------------------------------------
// Optimized blink function
// ------------------------------------------------------
async function blinkRelay(relayId, name, times = 2) {
  if (blinkingRelays.has(relayId)) {
    console.warn(`?? Relay ${relayId} is already blinking, skip.`);
    return;
  }
  
  if (currentBlinking >= MAX_PARALLEL_BLINK) {
    console.warn(`?? Relay ${relayId} skip blink, slot penuh (${currentBlinking}/${MAX_PARALLEL_BLINK}).`);
    return;
  }

  blinkingRelays.set(relayId, { name, startTime: Date.now() });
  currentBlinking++;

  try {
    const [[{ status: initialStatus }]] = await db.query(
      "SELECT status FROM relay WHERE id = ?",
      [relayId]
    );
    
    console.log(`?? Starting blink for relay ${relayId} (${name}), initial: ${initialStatus ? 'ON' : 'OFF'}`);
    
    let step = 0;
    const totalToggles = times * 2;
    let currentState = initialStatus === 1;

    async function doToggle() {
      if (step < totalToggles) {
        const action = currentState ? "off" : "on";
        
        try {
          await relayQueue.enqueue(relayId, action, 3); // High priority for blink
          console.log(`?? Blink ${relayId}: ${action.toUpperCase()} (${step + 1}/${totalToggles})`);
        } catch (err) {
          console.error(`? Blink toggle failed for relay ${relayId}:`, err.message);
        }

        currentState = !currentState;
        step++;
        
        setTimeout(doToggle, 3000);
      } else {
        // Final restore
        setTimeout(async () => {
          try {
            const restoreAction = initialStatus === 1 ? "on" : "off";
            await relayQueue.enqueue(relayId, restoreAction, 3);
            await db.query("UPDATE relay SET status = ? WHERE id = ?", [initialStatus, relayId]);
            
            // Update lastSentState untuk konsistensi
            lastSentState.set(relayId, Boolean(initialStatus));
            
            await insertRelayLog({
              relayNumber: relayId,
              action: restoreAction.toUpperCase(),
              status: "SUCCESS"
            });
            console.log(`? Blink completed for relay ${relayId}, restored to ${restoreAction.toUpperCase()}`);
          } catch (err) {
            console.error(`? Restore failed for relay ${relayId}:`, err.message);
          } finally {
            currentBlinking--;
            blinkingRelays.delete(relayId);
          }
        }, 3000);
      }
    }

    doToggle();
    
  } catch (err) {
    console.error(`? Blink init failed for relay ${relayId}:`, err.message);
    currentBlinking--;
    blinkingRelays.delete(relayId);
  }
}

// ------------------------------------------------------
// Graceful init dengan batching
// ------------------------------------------------------
(async function init() {
  console.log("?? Initializing relay states...");
  try {
    const [allRelays] = await db.query("SELECT id, relay_name, status FROM relay WHERE id BETWEEN 1 AND 32 ORDER BY id");
    
    console.log(`Found ${allRelays.length} relays to initialize`);
    
    // Process in small batches
    const batchSize = 4;
    for (let i = 0; i < allRelays.length; i += batchSize) {
      const batch = allRelays.slice(i, i + batchSize);
      
      // Process batch with delays
      for (let j = 0; j < batch.length; j++) {
        const relay = batch[j];
        setTimeout(async () => {
          try {
            lastSentState.set(relay.id, Boolean(relay.status));
            await relayQueue.enqueue(relay.id, relay.status ? "on" : "off", 0);
            console.log(`? Init relay ${relay.id} (${relay.relay_name}) to ${relay.status ? 'ON' : 'OFF'}`);
          } catch (err) {
            console.error(`? Init failed for relay ${relay.id}:`, err.message);
          }
        }, j * 200);
      }
      
      // Wait between batches
      if (i + batchSize < allRelays.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log("? Relay initialization complete");
  } catch (e) {
    console.error("? Init error:", e.message);
  }
})();

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('?? Shutting down relay cron...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('?? Shutting down relay cron...');
  process.exit(0);
});

// Export
module.exports = {
  manualOnGrace,
  blinkingRelays,
  lastSentState
};


