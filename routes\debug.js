// routes/debug.js - API untuk debugging relay blinking issues

const express = require('express');
const router = express.Router();
const db = require('../config/db');

// Import fungsi dari relaycron
const {
  forceClearBlinkingRelay,
  logBlinkingStatus,
  getBlinkingRelays
} = require('../jobs/relayCron');

/**
 * GET /api/debug/blinking-relays
 * Menampilkan status semua relay yang sedang blinking
 */
router.get('/blinking-relays', (req, res) => {
  try {
    const blinkingMap = getBlinkingRelays();
    const result = [];

    blinkingMap.forEach((data, relay_id) => {
      const durationMs = Date.now() - data.startTime;
      result.push({
        relay_id: relay_id,
        relay_name: data.relay_name,
        times: data.times,
        duration_seconds: Math.round(durationMs / 1000),
        start_time: new Date(data.startTime).toISOString()
      });
    });

    return res.json({
      success: true,
      count: result.length,
      data: result
    });
  } catch (error) {
    console.error('[DEBUG] Error getting blinking relays status:', error);
    return res.status(500).json({
      success: false,
      message: 'Error retrieving blinking relays status',
      error: error.message
    });
  }
});

/**
 * POST /api/debug/force-clear-relay
 * Memaksa membersihkan relay dari blinking state
 * Body: { relay_id: number, relay_name: string }
 */
router.post('/force-clear-relay', async (req, res) => {
  try {
    const { relay_id, relay_name } = req.body;

    if (relay_id == null || !relay_name) {
      return res.status(400).json({
        success: false,
        message: 'relay_id and relay_name are required in request body'
      });
    }

    // Panggil fungsi forceClearBlinkingRelay
    forceClearBlinkingRelay(relay_id, relay_name);

    return res.json({
      success: true,
      message: `Relay ${relay_name} (ID: ${relay_id}) has been forcefully cleared from blinking state.`
    });
  } catch (error) {
    console.error('[DEBUG] Error force-clearing relay:', error);
    return res.status(500).json({
      success: false,
      message: 'Error force-clearing relay',
      error: error.message
    });
  }
});

/**
 * POST /api/debug/log-blinking
 * Memaksa mencetak status blinkingRelays ke console (untuk keperluan debugging manual)
 */
router.post('/log-blinking', (req, res) => {
  try {
    logBlinkingStatus();
    return res.json({
      success: true,
      message: 'Blinking status logged to console.'
    });
  } catch (error) {
    console.error('[DEBUG] Error logging blinking status:', error);
    return res.status(500).json({
      success: false,
      message: 'Error logging blinking status',
      error: error.message
    });
  }
});

module.exports = router;
