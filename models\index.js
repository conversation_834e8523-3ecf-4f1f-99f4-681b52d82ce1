// models/index.js
const Sequelize = require('sequelize');
const config    = require('../config/db');
const sequelize = new Sequelize(config.database, config.username, config.password, config);

const db = {};
db.Sequelize = Sequelize;
db.sequelize  = sequelize;

db.MenuItem        = require('./menu_item')(sequelize, Sequelize.DataTypes);
db.FoodOrder       = require('./food_order')(sequelize, Sequelize.DataTypes);
db.FoodOrderItem   = require('./food_order_item')(sequelize, Sequelize.DataTypes);
// ... other models ...

Object.values(db).forEach(model => {
  if (model.associate) model.associate(db);
});

module.exports = db;
