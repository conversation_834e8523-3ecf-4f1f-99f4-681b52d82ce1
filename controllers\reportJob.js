// reporter.js
require('dotenv').config();
const cron       = require('node-cron');
const nodemailer = require('nodemailer');
const db         = require('../config/db'); // MySQL2 pool

// Setup Gmail transporter
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.GMAIL_USER,
    pass: process.env.GMAIL_PASS
  }
});

// Format headline dates as dd-MMM-yyyy, e.g. 02-Jul-2025
function formatHeadlineDate(d) {
  const dd = String(d.getDate()).padStart(2, '0');
  const mmmArr = ['Jan','Feb','Mar','Apr','Mei','Jun','Jul','Agu','Sep','Okt','Nov','Des'];
  const mmm = mmmArr[d.getMonth()];
  const yyyy = d.getFullYear();
  return `${dd}-${mmm}-${yyyy}`;
}

// Format table cell dates as dd-MMM (weekday), e.g. 02-Jul (Rabu)
function formatCellDate(d) {
  const dd = String(d.getDate()).padStart(2, '0');
  const mmmArr = ['Jan','Feb','Mar','Apr','Mei','Jun','Jul','Agu','Sep','Okt','Nov','Des'];
  const mmm = mmmArr[d.getMonth()];
  const weekday = d.toLocaleDateString('id-ID', { weekday: 'long' });
  return `${dd}-${mmm} (${weekday})`;
}

/**
 * Generates an HTML table summarizing each "business date" between startDate and endDate inclusive.
 * For each date D, it sums all data from D 08:00 → (D+1) 03:00.
 */
async function generateReportHtml(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const rows = [];

  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    const iso = d.toISOString().slice(0, 10);
    const next = new Date(d);
    next.setDate(d.getDate() + 1);
    const isoNext = next.toISOString().slice(0, 10);

    const windowStart = `${iso} 08:00:00`;
    const windowEnd = `${isoNext} 03:00:00`;

    // Ambil list session_id yang masuk ke window bisnis
    const [sessionRows] = await db.query(`
      SELECT s.id, s.payment_method, s.total_billiard, s.tax_amount
      FROM sessions s
      JOIN billiard_orders bo ON bo.session_id = s.id
      WHERE (
        CASE
          WHEN bo.start_time < '03:00:00'
            THEN CONCAT(DATE_ADD(s.date, INTERVAL 1 DAY), ' ', bo.start_time)
          ELSE CONCAT(s.date, ' ', bo.start_time)
        END
      ) >= ? AND (
        CASE
          WHEN bo.start_time < '03:00:00'
            THEN CONCAT(DATE_ADD(s.date, INTERVAL 1 DAY), ' ', bo.start_time)
          ELSE CONCAT(s.date, ' ', bo.start_time)
        END
      ) < ?
      GROUP BY s.id
    `, [windowStart, windowEnd]);

    const sessionIds = sessionRows.map(r => r.id);
    const idList = sessionIds.length ? sessionIds.join(',') : 'NULL';

    // Aggregate dari sessions (billiard dan service charge)
    let dejavu_cash = 0, dejavu_tf = 0, service_cash = 0, service_tf = 0;
    sessionRows.forEach(s => {
      if (s.payment_method === 'cash') {
        dejavu_cash += s.total_billiard || 0;
        service_cash += s.tax_amount || 0;
      } else if (s.payment_method === 'qris') {
        dejavu_tf += s.total_billiard || 0;
        service_tf += s.tax_amount || 0;
      }
    });

    // Aggregate dari makanan
    const [foodRows] = await db.query(`
      SELECT
        s.payment_method,
        f.name,
        SUM(i.total_amount) AS total
      FROM food_order_items i
      JOIN foods f ON f.id = i.food_id
      JOIN sessions s ON s.id = i.session_id
      WHERE s.id IN (${idList})
      GROUP BY s.payment_method, f.name
    `);

    let food_cash = 0, food_tf = 0, gloves_cash = 0, gloves_tf = 0;
    for (const row of foodRows) {
      const isGlove = row.name === 'Sarung Tangan';
      if (row.payment_method === 'cash') {
        if (isGlove) gloves_cash += row.total;
        else food_cash += row.total;
      } else if (row.payment_method === 'qris' || row.payment_method === 'debit') {
        if (isGlove) gloves_tf += row.total;
        else food_tf += row.total;
      }
    }

    const total =
      dejavu_cash + dejavu_tf +
      food_cash + food_tf +
      service_cash + service_tf +
      gloves_cash + gloves_tf;

    rows.push({
      date: formatCellDate(d),
      dejavu_cash, dejavu_tf,
      food_cash, food_tf,
      service_cash, service_tf,
      gloves_cash, gloves_tf,
      total
    });
  }

  const total = rows.reduce((acc, r) => {
    Object.keys(r).forEach(k => {
      if (k !== 'date') acc[k] = (acc[k] || 0) + r[k];
    });
    return acc;
  }, {});

  // Build HTML
  let html = `<h2>Report — ${formatHeadlineDate(new Date(startDate))}`;
  if (startDate !== endDate) {
    html += ` to ${formatHeadlineDate(new Date(endDate))}`;
  }
  html += `</h2>\n`;

  html += `<table border="1" cellpadding="4" cellspacing="0">\n<thead><tr>\n`;
  ['Tanggal', 'Dejavu Cash', 'Dejavu TF', 'Food Cash', 'Food TF', 'Service Charge Cash', 'Service Charge TF', 'Sarung Tangan Cash', 'Sarung Tangan TF', 'Total per Hari']
    .forEach(th => { html += `<th>${th}</th>\n`; });
  html += `</tr></thead><tbody>\n`;

  rows.forEach(r => {
    html += `<tr>
<td>${r.date}</td>
<td>${r.dejavu_cash.toLocaleString('id')}</td>
<td>${r.dejavu_tf.toLocaleString('id')}</td>
<td>${r.food_cash.toLocaleString('id')}</td>
<td>${r.food_tf.toLocaleString('id')}</td>
<td>${r.service_cash.toLocaleString('id')}</td>
<td>${r.service_tf.toLocaleString('id')}</td>
<td>${r.gloves_cash.toLocaleString('id')}</td>
<td>${r.gloves_tf.toLocaleString('id')}</td>
<td>${r.total.toLocaleString('id')}</td>
</tr>\n`;
  });

  html += `<tr style="font-weight:bold">
<td>Total (s.d. ${endDate})</td>
<td>${total.dejavu_cash.toLocaleString('id')}</td>
<td>${total.dejavu_tf.toLocaleString('id')}</td>
<td>${total.food_cash.toLocaleString('id')}</td>
<td>${total.food_tf.toLocaleString('id')}</td>
<td>${total.service_cash.toLocaleString('id')}</td>
<td>${total.service_tf.toLocaleString('id')}</td>
<td>${total.gloves_cash.toLocaleString('id')}</td>
<td>${total.gloves_tf.toLocaleString('id')}</td>
<td>${total.total.toLocaleString('id')}</td>
</tr>\n`;

  html += `</tbody></table>\n`;
  return html;
}


// sendDailyReport: from day 1 to yesterday of current month
async function sendDailyReport() {
  const now = new Date();
  const yd  = new Date(now);
  yd.setDate(now.getDate() - 1);

  const year  = yd.getFullYear();
  const month = String(yd.getMonth() + 1).padStart(2, '0');
  const day   = String(yd.getDate()).padStart(2, '0');

  const first = `${year}-${month}-01`;
  const last  = `${year}-${month}-${day}`;

  const htmlContent = await generateReportHtml(first, last);
  await transporter.sendMail({
    from:    `"Bilyard System" <${process.env.GMAIL_USER}>`,
    to:      process.env.REPORT_RECIPIENT,
    subject: `Report Harian ${year}-${month}`,
    html:    htmlContent
  });

  console.log(`✅ Daily report sent for ${year}-${month} (1→${day})`);
}

// sendMonthlyReport: full current month
async function sendMonthlyReport() {
  const now   = new Date();
  const year  = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');

  const first = `${year}-${month}-01`;
  const lastD = new Date(year, now.getMonth() + 1, 0).getDate();
  const last  = `${year}-${month}-${String(lastD).padStart(2, '0')}`;

  const htmlContent = await generateReportHtml(first, last);
  await transporter.sendMail({
    from:    `"Bilyard System" <${process.env.GMAIL_USER}>`,
    to:      process.env.REPORT_RECIPIENT,
    subject: `Report Bulanan ${year}-${month}`,
    html:    htmlContent
  });

  console.log(`✅ Monthly report sent for ${year}-${month}`);
}

// Cron schedules
cron.schedule('0 3 * * *', () => sendDailyReport().catch(console.error), { timezone: 'Asia/Makassar' });
// test setiap 1 menit:
// cron.schedule('* * * * *', () => sendDailyReport(), { timezone: 'Asia/Makassar' });

// cron.schedule('0 4 1 * *', () => sendMonthlyReport().catch(console.error), { timezone: 'Asia/Jakarta' });

module.exports = { sendDailyReport };
