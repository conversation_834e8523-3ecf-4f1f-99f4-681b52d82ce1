const db = require("../config/db");
const { sendRelayCommandHelper } = require("../controllers/relayController");
const axios = require("axios");
const BASE_URL = process.env.BASE_API_URL || "http://localhost:4400";


exports.createOrder = async (req, res) => {
  const connection = await db.getConnection();
  try {
    await connection.beginTransaction();

    const {
      guest_name,
      guest_phone,
      table_id,
      shift_id,
      package_id,
      discount,
      waiting_position,
      start_time,
      status, // 0 = Menunggu, 1 = Aktif
      order_type, // 0 = Tamu, 1 = Pegawai
    } = req.body;

    // ✅ Ambil harga dan durasi dari paket
    const [packageData] = await connection.query(
      "SELECT durasi_jam, harga FROM packages WHERE id = ?",
      [package_id]
    );

    if (packageData.length === 0) {
      return res.status(400).json({ error: "Paket tidak ditemukan" });
    }

    const { durasi_jam, harga } = packageData[0];

    // ✅ Hitung `end_time`
    const end_time = start_time
      ? new Date(new Date(start_time).getTime() + durasi_jam * 60 * 60 * 1000)
      : null;

    // ✅ Simpan order ke database
    const [result] = await connection.query(
      `INSERT INTO billiard_orders (guest_name, guest_phone, table_id, shift_id, package_id, duration, price, discount, status, waiting_position, start_time, end_time, order_type)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        guest_name,
        guest_phone || null,
        table_id || null,
        shift_id,
        package_id,
        durasi_jam,
        harga,
        discount || 0,
        status,
        waiting_position || null,
        start_time ? new Date(start_time) : null,
        end_time,
        order_type ?? 0,
      ]
    );

    const orderId = result.insertId;

    // ✅ Jika status aktif (1), nyalakan relay dan ubah status meja
    if (status === 1 && table_id) {
      // 🔹 Ambil relay ID berdasarkan table ID
      const [relayData] = await connection.query(
        "SELECT relay_id FROM billiard_tables WHERE id = ?",
        [table_id]
      );

      if (relayData.length > 0 && relayData[0].relay_id) {
        const relayId = relayData[0].relay_id;

        console.log(
          `[DEBUG] Relay ID ditemukan: ${relayId} untuk meja ${table_id}`
        );

        //await axios.post(`${BASE_URL}/api/relays/${relayId}/send`, { action: 'on' });
        // await sendRelayCommandHelper(relayId, "on");

        // 🔹 Update status relay di database
        await connection.query("UPDATE relay SET status = 1 WHERE id = ?", [
          relayId,
        ]);

        console.log(`[DB] Status relay ${relayId} diubah menjadi 1.`);
      } else {
        console.log(`[ERROR] Meja ${table_id} tidak memiliki relay.`);
      }

      // ✅ Update status meja menjadi "terpakai"
      await connection.query(
        "UPDATE billiard_tables SET status = 1 WHERE id = ?",
        [table_id]
      );
    }

    await connection.commit();
    res.status(201).json({
      id: orderId,
      message:
        "Order berhasil dibuat, meja ditandai sebagai terpakai, dan relay diaktifkan",
    });
  } catch (error) {
    await connection.rollback();
    console.error("[ORDER ERROR] Gagal membuat order:", error);
    res.status(500).json({ error: error.message });
  } finally {
    connection.release();
  }
};

/**
 * Get all billiard_orders with shift and package details
 * @route GET /api/billiard_orders
 */
exports.getAllOrders = async (req, res) => {
  try {
    const [rows] = await db.query(
      `SELECT o.*, s.nama_shift, p.nama_paket, b.table_number 
      FROM billiard_orders o
      JOIN rental_shifts s ON o.shift_id = s.id
      JOIN packages p ON o.package_id = p.id
      LEFT JOIN billiard_tables b ON o.table_id = b.id
      ORDER BY o.created_at DESC`
    );

    const currentTime = new Date();

    const enriched = rows.map((order) => {
      let waitingTimeSeconds = 0;

      const prev = rows.find(
        (other) =>
          other.table_id === order.table_id &&
          other.id !== order.id &&
          other.status === 1 &&
          new Date(other.end_time) > currentTime &&
          new Date(other.end_time) <= new Date(order.start_time)
      );

      if (prev) {
        const waitUntil = new Date(prev.end_time);
        const diff = (waitUntil - currentTime) / 1000;
        waitingTimeSeconds = diff > 0 ? Math.floor(diff) : 0;
      }

      return {
        ...order,
        waiting_time_seconds: waitingTimeSeconds,
      };
    });

    res.json(enriched);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Get order by ID
 * @route GET /api/orders/:id
 */
exports.getOrderById = async (req, res) => {
  try {
    const [rows] = await db.query(
      `SELECT o.*, s.nama_shift, p.nama_paket, b.table_number 
      FROM billiard_orders o
      JOIN rental_shifts s ON o.shift_id = s.id
      JOIN packages p ON o.package_id = p.id
      LEFT JOIN billiard_tables b ON o.table_id = b.id
      WHERE o.id = ?`,
      [req.params.id]
    );

    if (rows.length === 0)
      return res.status(404).json({ message: "Order tidak ditemukan" });
    res.json(rows[0]);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// exports.updateOrder = async (req, res) => {
//   const connection = await db.getConnection();
//   try {
//     await connection.beginTransaction();

//     const {
//       guest_name,
//       guest_phone,
//       table_id,
//       shift_id,
//       package_id,
//       discount,
//       status,
//       waiting_position,
//       start_time,
//       order_type,
//     } = req.body;

//     // ✅ Ambil harga dan durasi baru jika paket berubah
//     const [packageData] = await connection.query(
//       "SELECT durasi_jam, harga FROM packages WHERE id = ?",
//       [package_id]
//     );

//     if (packageData.length === 0) {
//       return res.status(400).json({ error: "Paket tidak ditemukan" });
//     }

//     const { durasi_jam, harga } = packageData[0];

//     // ✅ Hitung `end_time`
//     const end_time = start_time
//       ? new Date(new Date(start_time).getTime() + durasi_jam * 60 * 60 * 1000)
//       : null;

//     // ✅ Update order
//     await connection.query(
//       `UPDATE billiard_orders 
//       SET guest_name = ?, guest_phone = ?, table_id = ?, shift_id = ?, package_id = ?, duration = ?, price = ?, discount = ?, status = ?, waiting_position = ?, start_time = ?, end_time = ?, order_type = ?
//       WHERE id = ?`,
//       [
//         guest_name,
//         guest_phone || null,
//         table_id || null,
//         shift_id,
//         package_id,
//         durasi_jam,
//         harga,
//         discount || 0,
//         status,
//         waiting_position || null,
//         start_time ? new Date(start_time) : null,
//         end_time,
//         order_type ?? 0,
//         req.params.id,
//       ]
//     );

//     // ✅ Cek status baru dan nyalakan/matikan relay
//     if (table_id) {
//       const [relayData] = await connection.query(
//         "SELECT relay_id FROM billiard_tables WHERE id = ?",
//         [table_id]
//       );

//       if (relayData.length > 0 && relayData[0].relay_id) {
//         const relayId = relayData[0].relay_id;

//         console.log(
//           `[DEBUG] Relay ID ditemukan: ${relayId} untuk meja ${table_id}`
//         );

//         if (status === 1) {
//           //await sendRelayCommandHelper(relayId, "on");


//           await connection.query("UPDATE relay SET status = 1 WHERE id = ?", [
//             relayId,
//           ]);
//           console.log(`Relay ${relayId} diaktifkan.`);

//           await connection.query(
//             "UPDATE billiard_tables SET status = 1 WHERE id = ?",
//             [table_id]
//           );
//         } else if (status === 2) {
//           // 🔹 Matikan relay
//           //await sendRelayCommandHelper(relayId, "off");

//           await connection.query("UPDATE relay SET status = 0 WHERE id = ?", [
//             relayId,
//           ]);
//           console.log(`Relay ${relayId} dimatikan.`);
//         }
//       } else {
//         console.log(`[ERROR] Meja ${table_id} tidak memiliki relay.`);
//       }
//     }

//     await connection.commit();
//     res.json({ message: "Order berhasil diperbarui" });
//   } catch (error) {
//     await connection.rollback();
//     res.status(500).json({ error: error.message });
//   } finally {
//     connection.release();
//   }
// };

 exports.updateOrder = async (req, res) => {
   const connection = await db.getConnection();
   try {
     await connection.beginTransaction();

     const {
       guest_name,
       guest_phone,
       table_id,
       shift_id,
       package_id,
       discount = 0,
        status,
        paid = 0,            // ← terima jumlah yang dibayar di request body
       waiting_position,
       start_time,
       order_type,
    } = req.body;

     // Ambil durasi & harga
     const [packageData] = await connection.query(
       "SELECT durasi_jam, harga FROM packages WHERE id = ?",
       [package_id]
     );
     const { durasi_jam, harga } = packageData[0];
     const end_time = start_time
       ? new Date(new Date(start_time).getTime() + durasi_jam * 3600 * 1000)
       : null;

     // 1) Update billiard_orders seperti sekarang
     await connection.query(
       `UPDATE billiard_orders 
        SET guest_name=?, guest_phone=?, table_id=?, shift_id=?, package_id=?, duration=?, price=?, discount=?, status=?, waiting_position=?, start_time=?, end_time=?, order_type=?
        WHERE id = ?`,
       [
         guest_name,
         guest_phone || null,
         table_id || null,
         shift_id,
         package_id,
         durasi_jam,
         harga,
         discount || 0,
         status,
         waiting_position || null,
         start_time ? new Date(start_time) : null,
         end_time,
         order_type ?? 0,
         req.params.id,
       ]
     );

    // 2) Tambah paid ke sessions.pay_amount
    await connection.query(
      `UPDATE sessions
         SET pay_amount = COALESCE(pay_amount,0)  ?
       WHERE id = (
         SELECT session_id FROM billiard_orders WHERE id = ?
       )`,
      [paid, req.params.id]
    );

    // 3) Ambil pay_amount terkini & hitung balance
    const [[{ pay_amount = 0 }]] = await connection.query(
      `SELECT pay_amount 
         FROM sessions 
        WHERE id = (
          SELECT session_id FROM billiard_orders WHERE id = ?
        )`,
      [req.params.id]
    );
    const balance = harga - discount - pay_amount;

    // 4) Jika balance ≤ 0 → auto‐settle: complete order, matikan relay & meja
    if (balance <= 0 && table_id) {
      // a) tandai order selesai
      await connection.query(
        "UPDATE billiard_orders SET status = 2, updated_at = NOW() WHERE id = ?",
        [req.params.id]
      );

      // b) matikan relay di DB (dan hardware jika perlu)
      const [[{ relay_id }]] = await connection.query(
        "SELECT relay_id FROM billiard_tables WHERE id = ?",
        [table_id]
      );
      if (relay_id) {
        await connection.query("UPDATE relay SET status = 0 WHERE id = ?", [relay_id]);
        // await sendRelayCommandHelper(relay_id, "off");
      }

      // c) kosongkan meja
      await connection.query(
        "UPDATE billiard_tables SET status = 0 WHERE id = ?",
        [table_id]
      );

      await connection.commit();
      return res.json({
        message: "Order otomatis selesai (balance 0), relay dimatikan, meja dikosongkan."
      });
    }

     // 5) Jika belum lunas, lanjutkan flow lama (on/off relay berdasar status)
     if (table_id) {
       const [relayData] = await connection.query(
         "SELECT relay_id FROM billiard_tables WHERE id = ?",
         [table_id]
       );
       // … kode lama …
     }

     await connection.commit();
     res.json({ message: "Order berhasil diperbarui" });
   } catch (error) {
     await connection.rollback();
     res.status(500).json({ error: error.message });
   } finally {
     connection.release();
   }
 };


/**
 * Delete an order
 * @route DELETE /api/orders/:id
 */
exports.deleteOrder = async (req, res) => {
  try {
    // 1) Fetch order with its table & relay IDs
    const [existing] = await db.query(
      `SELECT 
         o.table_id, 
         t.relay_id 
       FROM billiard_orders o
       LEFT JOIN billiard_tables t 
         ON o.table_id = t.id
       WHERE o.id = ?`,
      [req.params.id]
    );

    if (existing.length === 0) {
      return res.status(404).json({ message: "Order tidak ditemukan" });
    }

    const { table_id, relay_id } = existing[0];

    // 2) Delete the order record
    await db.query(
      "DELETE FROM billiard_orders WHERE id = ?",
      [req.params.id]
    );

    // 3) Turn OFF the relay and update its status in the relay table
    if (relay_id) {
      // 3a) Update relay table status
      await db.query(
        "UPDATE relay SET status = 0 WHERE id = ?",
        [relay_id]
      );

      // 3b) Send OFF command to hardware (or simulate)
      //await sendRelayCommandHelper(relay_id, "off");

    }

    // 4) Mark the billiard table as free
    if (table_id) {
      await db.query(
        "UPDATE billiard_tables SET status = 0 WHERE id = ?",
        [table_id]
      );
      console.log(`✅ Meja ${table_id} dikosongkan.`);
    }

    // 5) Respond success
    return res.json({
      message: "Order berhasil dihapus; relay dimatikan; meja dikosongkan."
    });

  } catch (error) {
    console.error("[DELETE ORDER ERROR]", error);
    return res.status(500).json({ error: error.message });
  }
};


/**
 * ✅ **Complete Order (Manual)**
 * @route POST /api/orders/complete/:id
 * - Menyelesaikan order secara manual.
 * - Mematikan relay (jika ada).
 * - Mengosongkan meja billiard.
 */
exports.completeOrder = async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: "ID order harus disediakan." });
  }

  const connection = await db.getConnection();
  try {
    await connection.beginTransaction();

    // ✅ Cek apakah order ada dan aktif (status = 1)
    const [orderRows] = await connection.query(
      `SELECT o.id, o.table_id, t.relay_id, t.table_number 
      FROM billiard_orders o
      JOIN billiard_tables t ON o.table_id = t.id
      WHERE o.id = ? AND o.status = 1`,
      [id]
    );

    if (orderRows.length === 0) {
      return res
        .status(404)
        .json({ error: "Order tidak ditemukan atau sudah selesai." });
    }

    const order = orderRows[0];

    // ✅ Update order jadi "Completed" (status = 2)
    await connection.query("UPDATE billiard_orders SET status = 2 WHERE id = ?", [id]);

    // ✅ Kosongkan meja billiard
    if (order.table_id) {
      await connection.query(
        "UPDATE billiard_tables SET status = 0 WHERE id = ?",
        [order.table_id]
      );
      console.log(`✅ Meja ${order.table_id} dikosongkan.`);
    }

    // ✅ Matikan relay jika ada
    if (order.relay_id) {
      console.log(
        `🔻 Mematikan relay untuk meja ${order.table_number} (Relay ID: ${order.relay_id})`
      );

      // 🔹 Matikan relay di tabel relay
      await connection.query("UPDATE relay SET status = 0 WHERE id = ?", [
        order.relay_id,
      ]);
      console.log(
        `✅ Status relay ${order.relay_id} diubah menjadi 0 (OFF) di database.`
      );

      // 🔹 Kirim perintah OFF ke relay (jika pakai hardware)
      async function turnOffRelay(relayId) {
        //await sendRelayCommandHelper(order.relay_id, "off");
      }

    } else {
      console.log(
        `⚠️ Tidak ada relay untuk meja ${order.table_id}, hanya mengupdate status order.`
      );
    }

    await connection.commit();
    res.json({
      message: `✅ Order ${id} selesai, meja ${order.table_id} dikosongkan, dan relay dimatikan.`,
    });
  } catch (error) {
    await connection.rollback();
    console.error(`❌ Gagal menyelesaikan order ${id}:`, error);
    res.status(500).json({ error: error.message });
  } finally {
    connection.release();
  }
};


