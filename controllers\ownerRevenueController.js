const db = require('../config/db');

const getRevenueDashboard = async (req, res) => {
  try {
    // 1. Monthly Revenue (last 12 months)
    const [monthly] = await db.query(`
      SELECT 
        DATE_FORMAT(start_time, '%Y-%m') AS month,
        SUM(price - discount) AS total
      FROM orders
      WHERE status = 2 AND start_time IS NOT NULL
      GROUP BY month
      ORDER BY month DESC
      LIMIT 12
    `);

    // 2. List all finished orders (limit 100, latest first)
    const [orders] = await db.query(`
      SELECT 
        id, table_id, price, discount, start_time, end_time
      FROM orders
      WHERE status = 2 AND start_time IS NOT NULL
      ORDER BY start_time DESC
      LIMIT 100
    `);

    res.json({
      success: true,
      data: {
        monthly_revenue: monthly.map(row => ({
          month: row.month,
          total: Number(row.total || 0)
        })),
        orders: orders.map(order => ({
          ...order,
          price: Number(order.price || 0),
          discount: Number(order.discount || 0)
        }))
      }
    });
  } catch (err) {
    console.error('Revenue Dashboard Error:', err);
    res.status(500).json({ success: false, message: 'Internal Server Error' });
  }
};

module.exports = { getRevenueDashboard };
