const db = require("../config/db");

const getAll = async (req, res) => {
  const conn = await db.getConnection();

  try {
    const [rows] = await conn.query(`SELECT * FROM settings`);

    res.json(rows);
  } catch (err) {
    console.error(err);

    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

const update = async (req, res) => {
  const { id, value } = req.body;
  const conn = await db.getConnection();

  try {
    await conn.beginTransaction();

    const [r] = await conn.query(`UPDATE settings SET value = ? WHERE id = ?`, [
      value,
      id,
    ]);

    if (r.affectedRows === 0) {
      await conn.rollback();

      return res.status(404).json({ error: "Setting not found" });
    }

    await conn.commit();

    res.json({ success: true });
  } catch (err) {
    await conn.rollback();

    console.error(err);

    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

module.exports = { getAll, update };
