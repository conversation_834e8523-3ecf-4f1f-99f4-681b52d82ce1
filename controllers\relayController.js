

// controllers/relayController.js

const db = require('../config/db');
const net = require('net');
const { relayQueue } = require('../services/relayQueue');
const { manualOnGrace } = require('../jobs/relayCron');


const connectionPool = new Map();
const CONNECTION_TIMEOUT = 2500; // ms
const PORT = 80;

/**
 * Get or create a persistent TCP socket for a relay
 */
// async function getRelaySocket(relayId) {
//   let socket = connectionPool.get(relayId);
//   if (socket && !socket.destroyed && socket.writable) {
//     return socket;
//   }

//   // Fetch IP and name from DB
//   const [rows] = await db.query(
//     'SELECT ip_address, relay_name FROM relay WHERE id = ?',
//     [relayId]
//   );
//   if (rows.length === 0) {
//     throw new Error(`Relay ${relayId} tidak ditemukan`);
//   }
//   const { ip_address, relay_name } = rows[0];
//   if (!ip_address) {
//     throw new Error(`Relay ${relay_name} tidak memiliki IP address`);
//   }

//   // Create new socket
//   socket = new net.Socket();
//   socket.setKeepAlive(true);
//   socket.setNoDelay(true);
//   socket.setTimeout(CONNECTION_TIMEOUT);

//   await new Promise((resolve, reject) => {
//     socket.once('error', reject);
//     socket.connect(PORT, ip_address, () => {
//       socket.removeAllListeners('error');
//       // Store in pool and auto-clean
//       connectionPool.set(relayId, socket);
//       socket.once('close', () => connectionPool.delete(relayId));
//       socket.once('timeout', () => socket.destroy());
//       socket.once('error', () => socket.destroy());
//       resolve();
//     });
//   });

//   return socket;
// }

async function getRelaySocket(relayId) {
  // kalau sudah ada di pool, pakai itu
  let socket = connectionPool.get(relayId);
  if (socket && !socket.destroyed && socket.writable) {
    return socket;
  }

  // baca IP dari DB (sama seperti sebelumnya)
  const [rows] = await db.query(
    'SELECT ip_address, relay_name FROM relay WHERE id = ?',
    [relayId]
  );
  if (!rows.length) throw new Error(`Relay ${relayId} tidak ditemukan`);
  const { ip_address, relay_name } = rows[0];
  if (!ip_address) throw new Error(`Relay ${relay_name} tidak memiliki IP address`);

  socket = new net.Socket();
  socket.setKeepAlive(true);
  socket.setNoDelay(true);

  // helper: connect dengan timeout manual
  function connectWithTimeout(port, host, timeout) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        socket.destroy();
        reject(new Error('Connection timeout'));
      }, timeout);

      socket.once('error', err => {
        clearTimeout(timer);
        reject(err);
      });

      socket.connect(port, host, () => {
        clearTimeout(timer);
        socket.removeAllListeners('error');
        resolve();
      });
    });
  }

  // pakai connectWithTimeout alih‐alih langsung socket.connect
  await connectWithTimeout(PORT, ip_address, CONNECTION_TIMEOUT);

  // kalau sukses, simpan di pool seperti biasa
  connectionPool.set(relayId, socket);
  socket.once('close', () => connectionPool.delete(relayId));
  socket.once('timeout', () => socket.destroy());
  socket.once('error', () => socket.destroy());

  return socket;
}


/**
 * Send command directly over TCP (fallback or sync mode)
 */
async function sendRelayCommandDirect(relayId, action) {
  const socket = await getRelaySocket(relayId);
  const command = action === 'on' ? `n${relayId}` : `f${relayId}`;
  return new Promise((resolve, reject) => {
    socket.write(command + '\n', err => {
      if (err) return reject(err);
      resolve({ success: true, command });
    });
  });
}

/**
 * Enqueue command via shared relayQueue
 */
// async function sendRelayCommandHelper(relayId, action) {
//   // lower priority for ON (0), higher for OFF (1)
//   const priority = action === 'off' ? 1 : 0;
//   return relayQueue.enqueue(relayId, action, priority);
// }

// async function sendRelayCommandHelper(relayId, action) {
//   const priority = action === 'off' ? 1 : 0;
//   // enqueue returns a commandObj with a timestamp
//   const commandObj = relayQueue.enqueue(relayId, action, priority);

//   // now return a promise that resolves/rejects when queue emits
//   return new Promise((resolve, reject) => {
//     const doneEvt = `completed_${relayId}_${commandObj.timestamp}`;
//     const failEvt = `failed_${relayId}_${commandObj.timestamp}`;

//     // safety timeout
//     const to = setTimeout(() => {
//       relayQueue.removeAllListeners(doneEvt);
//       relayQueue.removeAllListeners(failEvt);
//       reject(new Error('Relay queue timed out'));
//     }, 5000);

//     relayQueue.once(doneEvt, result => {
//       clearTimeout(to);
//       resolve(result);
//     });

//     relayQueue.once(failEvt, err => {
//       clearTimeout(to);
//       reject(err);
//     });
//   });
// }

/**
 * Enqueue command via shared queue and return its Promise directly.
 */
async function sendRelayCommandHelper(relayId, action) {
  const priority = action === 'off' ? 1 : 0;
  // langsung kembalikan Promise dari enqueue
  return relayQueue.enqueue(relayId, action, priority);
}


exports.sendRelayCommandHelper = sendRelayCommandHelper;

/**
 * GET /api/relays
 */
exports.getAllRelays = async (req, res) => {
  try {
    const [relays] = await db.query(`
      SELECT id, relay_name, status, ip_address, hardware_id
      FROM relay
      ORDER BY id ASC
    `);
    res.json(relays);
  } catch (err) {
    console.error('Error fetching relays:', err);
    res.status(500).json({ error: 'Gagal mengambil data relay' });
  }
};

/**
 * GET /api/relays/:id
 */
exports.getRelayById = async (req, res) => {
  try {
    const [rows] = await db.query(
      'SELECT id, relay_name, status, ip_address, hardware_id FROM relay WHERE id = ?',
      [req.params.id]
    );
    if (rows.length === 0) {
      return res.status(404).json({ error: 'Relay tidak ditemukan' });
    }
    res.json(rows[0]);
  } catch (err) {
    console.error('Error fetching relay:', err);
    res.status(500).json({ error: 'Gagal mengambil data relay' });
  }
};

/**
 * POST /api/relays
 */
exports.createRelay = async (req, res) => {
  try {
    const { relay_name, status, ip_address } = req.body;
    if (!relay_name || !ip_address) {
      return res
        .status(400)
        .json({ error: 'Nama relay dan IP address wajib diisi' });
    }
    const [result] = await db.query(
      `INSERT INTO relay
         (relay_name, status, ip_address, created_at, updated_at)
       VALUES (?, ?, ?, NOW(), NOW())`,
      [relay_name, status ?? 0, ip_address]
    );
    res.status(201).json({
      id: result.insertId,
      message: 'Relay berhasil ditambahkan'
    });
  } catch (err) {
    console.error('Error creating relay:', err);
    res.status(500).json({ error: 'Gagal menambahkan relay' });
  }
};

/**
 * PUT /api/relays/:id
 */
exports.updateRelay = async (req, res) => {
  try {
    const { relay_name, status, ip_address } = req.body;
    if (!relay_name || !ip_address) {
      return res
        .status(400)
        .json({ error: 'Nama relay dan IP address wajib diisi' });
    }
    const [result] = await db.query(
      `UPDATE relay
         SET relay_name = ?, status = ?, ip_address = ?, updated_at = NOW()
       WHERE id = ?`,
      [relay_name, status ?? 0, ip_address, req.params.id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Relay tidak ditemukan' });
    }
    res.json({ message: 'Relay berhasil diperbarui' });
  } catch (err) {
    console.error('Error updating relay:', err);
    res.status(500).json({ error: 'Gagal memperbarui relay' });
  }
};

/**
 * DELETE /api/relays/:id
 */
exports.deleteRelay = async (req, res) => {
  try {
    const [result] = await db.query(
      'DELETE FROM relay WHERE id = ?',
      [req.params.id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Relay tidak ditemukan' });
    }
    res.json({ message: 'Relay berhasil dihapus' });
  } catch (err) {
    console.error('Error deleting relay:', err);
    res.status(500).json({ error: 'Gagal menghapus relay' });
  }
};

/**
 * POST /api/relays/:id/send
 */
exports.sendRelayCommand = async (req, res) => {
  const { id } = req.params;
  const { action } = req.body;
  if (!['on', 'off'].includes(action)) {
    return res.status(400).json({ error: "Aksi harus 'on' atau 'off'" });
  }

  try {
    // 1) Kirim perintah ke queue dan tunggu hasilnya
    const result = await sendRelayCommandHelper(id, action);

    // ————————————————
    // ← Tambahkan POIN 1 DI SINI:
    // Update status di DB sesuai action
    await db.query(
      "UPDATE relay SET status = ? WHERE id = ?",
      [ action === 'on' ? 1 : 0, id ]
    );

    // (opsional) set grace period untuk manual ON
    // const { manualOnGrace } = require('../services/relayCron');
    manualOnGrace.set(Number(id), Date.now());
    // ————————————————

    if (!result.success) {
      return res.status(502).json({ error: result.error });
    }
    return res.json(result);

  } catch (err) {
    console.error('Unexpected error in sendRelayCommand:', err);
    // fallback: kirim direct jika queue gagal
    try {
      const direct = await sendRelayCommandDirect(id, action);
      return res.json(direct);
    } catch (e) {
      console.error('Fallback direct send failed:', e);
      return res.status(500).json({ error: e.message });
    }
  }
};
