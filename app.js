// app.js
const express = require("express");
require("dotenv").config();
const cors = require("cors");
const morgan = require("morgan");
//const holidayRoutes   = require('./routes/holidays');

// Import helper Relay LAN
const { initRelayIPs, relayConnections } = require("./services/relayLan");

// Karena kita perlu menunggu initRelayIPs() selesai sebelum meload cron dan server,
// bungkus semua dalam IIFE async:
(async () => {
  // 1️⃣ Inisialisasi koneksi LAN ke semua relay
  try {
    console.log("🔌 Inisialisasi Relay LAN...");
    await initRelayIPs();
    console.log(
      "📊 Status awal koneksi Relay LAN:",
      Array.from(relayConnections.entries())
    );
  } catch (err) {
    console.error("❌ Gagal inisialisasi Relay LAN:", err.message);
    // Jika memang fatal, kamu bisa:
    // process.exit(1);
  }

  // 2️⃣ Load cron-jobs setelah koneksi LAN siap
  require("./jobs/relayCron");
  require("./jobs/tableStatusCron");
  require("./jobs/orderCron");
  // jalankan cron job report
  require('./controllers/reportJob');


  // 3️⃣ Setup Express
  const app = express();

  // middleware
  app.use(express.json());
  app.use(cors());
  app.use(morgan("dev"));

  // register API routes
  app.use("/api/auth",               require("./routes/auth"));
  app.use("/api/orders",             require("./routes/orders"));
  app.use("/api/tables",             require("./routes/tables"));
  app.use("/api/dashboard",          require("./routes/dashboard"));
  app.use("/api/relays",             require("./routes/relay"));
  app.use("/api/relay-monitor",      require("./routes/relayMonitor"));
  app.use("/api/owners",             require("./routes/owner"));
  app.use("/api/menu_items",         require("./routes/menu_items"));
  app.use("/api/sessions",           require("./routes/sessions"));
  app.use("/api/settings",           require("./routes/settings"));
  app.use("/api/users",              require("./routes/users"));
  app.use("/api/reports",            require("./routes/reports"));
  app.use("/api/packages",           require("./routes/paket"));
  app.use("/api/type_package_prices",require("./routes/type_package_prices"));
  app.use("/api",                     require("./print"));
  app.use("/api/debug",              require("./routes/debug"));
  app.use('/api/holidays',           require('./routes/holidays'));

  // 404 handler
  app.use((req, res) => {
    res.status(404).json({ error: "Route not found", route: req.originalUrl });
  });

  // global error handler
  app.use((err, req, res, next) => {
    console.error("Unhandled Error:", err);
    res.status(500).json({ error: "Internal Server Error", details: err.message });
  });

  // 4️⃣ Start server
  const PORT = process.env.PORT || 4400;
  app.listen(PORT, () => console.log(`🚀 Server running on port ${PORT}`));
})();
