const jwt = require('jsonwebtoken');

// Use your JWT secret from your .env file or hardcode it for testing.
const secret = 'bfc9f9ef841f5db5b3ae26e1d179edc9f6f02177993575682457ab87a430a265';

// Define the payload you want to include in the token.
const payload = {
  id: 1,           // example user ID
  username: 'test' // example username
};

// Generate a token that expires in 2 hours.
const token = jwt.sign(payload, secret, { expiresIn: '2h' });

console.log('Generated JWT:', token);
