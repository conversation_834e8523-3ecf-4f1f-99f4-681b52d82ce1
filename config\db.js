const mysql = require('mysql2');
require('dotenv').config();

// Create MySQL connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || 'root',
  database: process.env.DB_NAME || 'bilyard',
  decimalNumbers: true,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Convert pool to promise-based API
const db = pool.promise();

// Test database connection
(async () => {
  try {
    const [rows] = await db.query('SELECT 1');
    console.log('[DATABASE] Connected successfully to MySQL!');
  } catch (err) {
    console.error('[DATABASE] Connection failed:', err.message);
  }
})();

module.exports = db;
