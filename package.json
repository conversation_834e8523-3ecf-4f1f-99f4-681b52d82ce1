{"name": "bilyard_backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "pm2 start app.js --watch --name dejavu-billiard && pm2 logs dejavu-billiard", "dev": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.6.1", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "log-timestamp": "^0.3.0", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.12.0", "node-cron": "^3.0.3", "nodemailer": "^7.0.4", "play-sound": "^1.1.6", "pm2": "^6.0.8", "readline": "^1.3.0", "serialport": "^13.0.0"}, "devDependencies": {"nodemon": "^3.1.9"}}