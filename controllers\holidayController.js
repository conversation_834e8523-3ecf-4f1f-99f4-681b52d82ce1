const db = require('../config/db');

exports.getAllHolidays = async (req, res) => {
  try {
    const [rows] = await db.query(`
      SELECT id,
             DATE_FORMAT(holiday_date, '%Y-%m-%d') AS holiday_date,
             name
        FROM holidays
       ORDER BY holiday_date ASC
    `);
    res.json(rows);
  } catch (err) {
    console.error('Error fetching holidays:', err);
    res.status(500).json({ error: err.message });
  }
};

exports.getHolidayById = async (req, res) => {
  const { id } = req.params;
  try {
    const [rows] = await db.query(
      `SELECT id,
              DATE_FORMAT(holiday_date, '%Y-%m-%d') AS holiday_date,
              name
         FROM holidays
        WHERE id = ?`,
      [id]
    );
    if (rows.length === 0) {
      return res.status(404).json({ message: 'Holiday not found' });
    }
    res.json(rows[0]);
  } catch (err) {
    console.error('Error fetching holiday by ID:', err);
    res.status(500).json({ error: err.message });
  }
};

exports.createHoliday = async (req, res) => {
  const { holiday_date, name } = req.body;
  console.log("REQ.BODY ▶︎", req.body);

  if (!holiday_date || !name) {
    return res.status(400).json({ message: 'holiday_date dan name diperlukan' });
  }

  try {
    const [result] = await db.query(
      `INSERT INTO holidays (holiday_date, name) VALUES (?, ?)`,
      [holiday_date, name]
    );
    res.status(201).json({
      id: result.insertId,
      holiday_date,
      name
    });
  } catch (err) {
    console.error('Error creating holiday:', err);
    res.status(500).json({ error: err.message });
  }
};

exports.updateHoliday = async (req, res) => {
  const { id } = req.params;
  const { holiday_date, name } = req.body;
  if (!holiday_date || !name) {
    return res.status(400).json({ message: 'holiday_date dan name diperlukan' });
  }

  try {
    const [result] = await db.query(
      `UPDATE holidays
          SET holiday_date = ?, name = ?
        WHERE id = ?`,
      [holiday_date, name, id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Holiday not found' });
    }
    res.json({ id, holiday_date, name });
  } catch (err) {
    console.error('Error updating holiday:', err);
    res.status(500).json({ error: err.message });
  }
};

exports.deleteHoliday = async (req, res) => {
  const { id } = req.params;
  try {
    const [result] = await db.query(
      `DELETE FROM holidays WHERE id = ?`,
      [id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Holiday not found' });
    }
    res.status(204).end();
  } catch (err) {
    console.error('Error deleting holiday:', err);
    res.status(500).json({ error: err.message });
  }
};
