const express = require("express");
const router = express.Router();
const db = require("../config/db");

router.get("/", async (req, res) => {
  try {
    const [orders] = await db.query(`
      SELECT 
        t.id AS table_id,
        t.table_number,
        t.type,
        o.status AS order_status,
        o.start_time,
        o.end_time,
        o.created_at,
        s.guest_name
      FROM billiard_tables t
      LEFT JOIN billiard_orders o ON t.id = o.table_id AND o.status IN (0, 1)
      LEFT JOIN sessions s ON o.session_id = s.id
      ORDER BY t.table_number ASC, o.created_at ASC
    `);

    const now = new Date();
    const todayStr = now.toISOString().split("T")[0];

    const grouped = {};

    for (const row of orders) {
      const key = row.table_number;
      if (!grouped[key]) {
        grouped[key] = {
          table_number: row.table_number,
          current_order: null,
          next_order: null,
          type: row.type,
          status: 0,
        };
      }

      const startDateTime = row.start_time
        ? new Date(`${todayStr}T${row.start_time}`)
        : null;

      let endDateTime = row.end_time
        ? new Date(`${todayStr}T${row.end_time}`)
        : null;

      if (startDateTime && endDateTime && endDateTime < startDateTime) {
        endDateTime.setDate(endDateTime.getDate() + 1);
      }

      if (
        !grouped[key].current_order &&
        startDateTime &&
        endDateTime &&
        now >= startDateTime &&
        now <= endDateTime
      ) {
        const remaining = Math.max(Math.floor((endDateTime - now) / 1000), 0);

        grouped[key].current_order = {
          guest_name: row.guest_name,
          start_time: row.start_time,
          end_time: row.end_time,
          remaining_time_seconds: remaining,
        };

        grouped[key].status = 1;
      } else if (
        row.order_status === 0 &&
        !grouped[key].next_order &&
        endDateTime &&
        endDateTime > now
      ) {
        grouped[key].next_order = {
          guest_name: row.guest_name,
          start_time: row.start_time,
          end_time: row.end_time,
        };
      }
    }

    res.json(Object.values(grouped));
  } catch (error) {
    console.error("Error fetching table orders:", error);
    res.status(500).json({ message: "Server error" });
  }
});

module.exports = router;
