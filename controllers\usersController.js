const db = require("../config/db");
const crypto = require('crypto');

function hashPassword(password) {
  return crypto.createHash("sha256").update(password).digest("hex");
}

const getAll = async (req, res) => {
  const conn = await db.getConnection();

  try {
    const [users] = await conn.query(`SELECT * FROM users`);

    res.json(users);
  } catch (err) {
    console.error(err);

    res.status(500).json({ error: err.message }); 
  } finally {
    conn.release();
  }
};

const getById = async (req, res) => {
  const { id } = req.params;

  const conn = await db.getConnection();

  try {
    const [user] = await conn.query(`SELECT * FROM users WHERE id = ?`, [id]);

    if (user.length === 0) {
      return res.status(404).json({ error: "User not found" });
    }

    res.json(user[0]);
  } catch (err) {
    console.error(err);

    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

const create = async (req, res) => {
  const { full_name, username, password } = req.body;

  const conn = await db.getConnection();

  try {
    await conn.beginTransaction();

    const [r] = await conn.query(
      `INSERT INTO users (full_name, username, password) VALUES (?, ?, ?)`,
      [full_name, username, hashPassword(password)]
    );

    await conn.commit();

    res.status(201).json({ id: r.insertId });
  } catch (err) {
    await conn.rollback();

    console.error(err);

    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

const update = async (req, res) => {
  const { id } = req.params;
  const { full_name, username, password } = req.body;

  const conn = await db.getConnection();

  try {
    await conn.beginTransaction();

    const [r] = await conn.query(
      `UPDATE users SET full_name = ?, username = ?, password = ? WHERE id = ?`,
      [full_name, username, hashPassword(password), id]
    );

    await conn.commit();

    if (r.affectedRows === 0) {
      await conn.rollback();

      return res.status(404).json({ error: "User not found" });
    }

    res.json({ success: true });
  } catch (err) {
    await conn.rollback();

    console.error(err);

    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

const remove = async (req, res) => {
  const { id } = req.params;

  const conn = await db.getConnection();

  try {
    await conn.beginTransaction();

    const [r] = await conn.query(`DELETE FROM users WHERE id = ?`, [id]);

    if (r.affectedRows === 0) {
      await conn.rollback();

      return res.status(404).json({ error: "User not found" });
    }

    await conn.commit();

    res.json({ success: true });
  } catch (err) {
    await conn.rollback();

    console.error(err);

    res.status(500).json({ error: err.message });
  } finally {
    conn.release();
  }
};

module.exports = {
  getAll,
  getById,
  create,
  update,
  remove,
};
