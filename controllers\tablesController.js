const db = require("../config/db");

exports.getAllTables = async (req, res) => {
  try {
    const [results] = await db.query(`
      SELECT t.*, r.relay_name, r.status AS relay_status
      FROM billiard_tables t
      LEFT JOIN relay r ON t.relay_id = r.id
    `);

    res.json(results);
  } catch (error) {
    console.error("[getAllTables] ❌ Error:", error.message);
    res.status(500).json({ error: error.message });
  }
};

exports.getTableById = async (req, res) => {
  const { id } = req.params;

  try {
    const [result] = await db.query(
      `SELECT t.*, r.relay_name, r.status AS relay_status
      FROM billiard_tables t
      LEFT JOIN relay r ON t.relay_id = r.id
      WHERE t.id = ?`,
      [id]
    );

    if (result.length === 0)
      return res.status(404).json({ message: "Table not found" });

    res.json(result[0]);
  } catch (error) {
    console.error("[getTableById] ❌ Error:", error.message);
    res.status(500).json({ error: error.message });
  }
};

exports.createTable = async (req, res) => {
  const { table_number, type, status, relay_id, price } = req.body;

  if (!table_number || !type || status == null || relay_id == null) {
    return res
      .status(400)
      .json({ message: "Nomor Meja, Tipe, Status, dan Relay ID harus diisi" });
  }

  try {
    const [result] = await db.query(
      "INSERT INTO billiard_tables (table_number, type, status, relay_id, price) VALUES (?, ?, ?, ?, ?)",
      [table_number, type, status, relay_id, price]
    );

    res.status(201).json({
      message: "Table created successfully",
      tableId: result.insertId,
    });
  } catch (error) {
    console.error("[createTable] ❌ Error:", error.message);
    res.status(500).json({ error: error.message });
  }
};

exports.updateTable = async (req, res) => {
  const { table_number, type, status, relay_id, price } = req.body;

  try {
    const [result] = await db.query(
      "UPDATE billiard_tables SET table_number = ?, type = ?, status = ?, relay_id = ?, price = ? WHERE id = ?",
      [table_number, type, status, relay_id, price, req.params.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: "Table not found" });
    }

    res.json({ message: "Table updated successfully", result });
  } catch (error) {
    console.error("[updateTable] ❌ Error:", error.message);
    res.status(500).json({ error: error.message });
  }
};

exports.deleteTable = async (req, res) => {
  try {
    const [result] = await db.query(
      "DELETE FROM billiard_tables WHERE id = ?",
      [req.params.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: "Table not found" });
    }

    res.json({ message: "Table deleted successfully" });
  } catch (error) {
    console.error("[deleteTable] ❌ Error:", error.message);
    res.status(500).json({ error: error.message });
  }
};

exports.updateRelayStatus = async (req, res) => {
  const { id } = req.params; // id = billiard_table.id
  const { relay_status } = req.body;

  if (relay_status == null) {
    return res.status(400).json({ message: "relay_status wajib diisi" });
  }

  try {
    // 1) Cari relay_id dari tabel billiard_tables
    const [rows] = await db.query(
      "SELECT relay_id FROM billiard_tables WHERE id = ?",
      [id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: "Meja tidak ditemukan" });
    }

    const relayId = rows[0].relay_id;

    if (!relayId) {
      return res.status(400).json({ message: "Meja ini tidak terhubung dengan relay" });
    }

    // 2) Update relay status
    await db.query("UPDATE relay SET status = ? WHERE id = ?", [
      relay_status,
      relayId,
    ]);

    res.json({ message: `Relay status berhasil diperbarui menjadi ${relay_status}` });
  } catch (error) {
    console.error("[updateRelayStatus] ❌ Error:", error.message);
    res.status(500).json({ error: error.message });
  }
};

