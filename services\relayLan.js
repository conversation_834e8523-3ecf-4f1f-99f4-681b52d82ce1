// services/relayLan.js
const db = require("../config/db");
const net = require("net");
const relayConnections = new Map();

async function initRelayIPs() {
  const [rows] = await db.query(`
    SELECT DISTINCT ip_address
    FROM relay
    WHERE ip_address IS NOT NULL AND ip_address != ''
  `);
  for (const { ip_address } of rows) {
    await checkRelayIp(ip_address);
  }
}

function checkRelayIp(ip, port = 80) {
  return new Promise(resolve => {
    const socket = new net.Socket();
    socket.setTimeout(3000);
    socket.connect(port, ip, () => {
      relayConnections.set(ip, "connected");
      socket.destroy();
      resolve();
    });
    socket.on("error", () => {
      relayConnections.set(ip, "offline");
      resolve();
    });
    socket.on("timeout", () => {
      relayConnections.set(ip, "offline");
      socket.destroy();
      resolve();
    });
  });
}

module.exports = { initRelayIPs, relayConnections };
