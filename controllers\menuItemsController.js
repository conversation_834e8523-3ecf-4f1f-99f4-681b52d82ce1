const db = require("../config/db");

const getAll = async (req, res) => {
  try {
    const [items] = await db.query("SELECT * FROM foods ORDER BY name ASC");

    res.json(items);
  } catch (error) {
    console.error(error);

    res.status(500).json({ error: error.message });
  }
};

const getById = async (req, res) => {
  const { id } = req.params;

  try {
    const item = await db.query("SELECT * FROM foods WHERE id = ?", [id]);

    if (!item) return res.status(404).json({ error: "Menu item not found" });

    res.json(item);
  } catch (error) {
    console.error(error);

    res.status(500).json({ error: error.message });
  }
};

const create = async (req, res) => {
  const { name, category, description, price, is_available, tax } = req.body;

  try {
    const [item] = await db.query(
      "INSERT INTO foods (name, category, description, price, is_available, tax) VALUES (?, ?, ?, ?, ?, ?)",
      [name, category, description, price, is_available, tax]
    );

    res.status(201).json(item);
  } catch (error) {
    console.error(error);

    res.status(500).json({ error: error.message });
  }
};

const update = async (req, res) => {
  const { id } = req.params;
  const { name, category, description, price, is_available, tax } = req.body;

  try {
    const [item] = await db.query(
      "UPDATE foods SET name = ?, category = ?, description = ?, price = ?, is_available = ?, tax = ? WHERE id = ?",
      [name, category, description, price, is_available, tax, id]
    );

    if (!item) return res.status(404).json({ error: "Menu item not found" });

    res.json(item);
  } catch (error) {
    console.error(error);

    res.status(500).json({ error: error.message });
  }
};

const remove = async (req, res) => {
  const { id } = req.params;

  try {
    const [item] = await db.query("DELETE FROM foods WHERE id = ?", [id]);

    if (!item) return res.status(404).json({ error: "Menu item not found" });

    res.json({ message: "Menu item deleted" });
  } catch (error) {
    console.error(error);

    res.status(500).json({ error: error.message });
  }
};

module.exports = {
  getAll,
  getById,
  create,
  update,
  remove,
};
